package contact

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"

	authmw "hopenbackend/pkg/middleware"
	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
)

// Service handles contact operations using ArangoDB
type Service struct {
	logger      *zap.Logger
	arangoDB    *database.ArangoDBClient
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	oryClient   *ory.Client
	httpClient  *http.Client
}

// Dependencies holds the dependencies for the contact service
type Dependencies struct {
	Logger      *zap.Logger
	ArangoDB    *database.ArangoDBClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
}

// New creates a new contact service instance
func New(deps *Dependencies) *Service {
	return &Service{
		logger:      deps.Logger,
		arangoDB:    deps.ArangoDB,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		oryClient:   deps.OryClient,
		httpClient:  &http.Client{Timeout: 10 * time.Second},
	}
}

// RegisterRoutes registers the contact service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	router.POST("/requests", s.authMiddleware(), s.sendContactRequest)
	router.POST("/requests/:id/accept", s.authMiddleware(), s.acceptContactRequest)
	router.POST("/requests/:id/decline", s.authMiddleware(), s.declineContactRequest)
	router.GET("/requests/:id", s.authMiddleware(), s.getContactRequest)
	router.GET("/requests/history", s.authMiddleware(), s.getContactRequestHistory)
	router.POST("/requests/expire-old", s.authMiddleware(), s.expireOldRequests)
	router.DELETE("/requests/:id", s.authMiddleware(), s.cancelContactRequest)
	router.GET("/contacts", s.authMiddleware(), s.getContacts)
	router.GET("/contacts/mutual", s.authMiddleware(), s.getMutualContacts)
	router.GET("/contacts/suggestions", s.authMiddleware(), s.getContactSuggestions)
	router.GET("/requests/sent", s.authMiddleware(), s.getSentRequests)
	router.GET("/requests/received", s.authMiddleware(), s.getReceivedRequests)
	router.DELETE("/:contactId", s.authMiddleware(), s.removeContact)
}

// Contact represents a contact relationship
type Contact struct {
	Key         string     `json:"_key,omitempty"`
	RequesterID string     `json:"requester_id"`
	RecipientID string     `json:"recipient_id"`
	Status      string     `json:"status"` // pending, accepted, declined
	Message     *string    `json:"message,omitempty"` // Optional message with contact request
	CreatedAt   time.Time  `json:"created_at"`
	AcceptedAt  *time.Time `json:"accepted_at,omitempty"`
}

// ContactRequest represents a contact request payload
type ContactRequest struct {
	RecipientID string  `json:"recipient_id" binding:"required"`
	Message     *string `json:"message,omitempty"` // Optional message with the request
}

// ContactResponse represents a contact in API responses
type ContactResponse struct {
	ID              string    `json:"id"`
	SenderId        string    `json:"senderId"`
	SenderName      string    `json:"senderName"`
	SenderAvatarUrl *string   `json:"senderAvatarUrl,omitempty"`
	ReceiverId      string    `json:"receiverId"`
	ReceiverName    string    `json:"receiverName"`
	ReceiverAvatarUrl *string `json:"receiverAvatarUrl,omitempty"`
	SentAt          time.Time `json:"sentAt"`
	Status          string    `json:"status"`
	Message         *string   `json:"message,omitempty"`
	RespondedAt     *time.Time `json:"respondedAt,omitempty"`
}

// sendContactRequest handles sending a contact request
func (s *Service) sendContactRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req ContactRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate recipient ID
	if _, err := uuid.Parse(req.RecipientID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recipient ID format"})
		return
	}

	// Check if user is trying to add themselves
	if userID.(string) == req.RecipientID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot send contact request to yourself"})
		return
	}

	// Rate limiting for contact requests
	allowed, err := s.rateLimiter.AllowSocialOperation(c.Request.Context(), userID.(string), "contact_request")
	if err != nil {
		s.logger.Error("Rate limit check failed", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if !allowed {
		c.JSON(http.StatusTooManyRequests, gin.H{"error": "Contact request rate limit exceeded"})
		return
	}

	// Check if contact relationship already exists
	exists, err := s.contactRelationshipExists(c.Request.Context(), userID.(string), req.RecipientID)
	if err != nil {
		s.logger.Error("Failed to check existing contact relationship", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if exists {
		c.JSON(http.StatusConflict, gin.H{"error": "Contact relationship already exists"})
		return
	}

	// Create contact request
	contact := &database.Contact{
		RequesterID: userID.(string),
		RecipientID: req.RecipientID,
		Status:      "pending",
		Message:     req.Message,
		CreatedAt:   time.Now(),
	}

	if err := s.arangoDB.CreateContact(c.Request.Context(), contact); err != nil {
		s.logger.Error("Failed to create contact request",
			zap.String("requester_id", userID.(string)),
			zap.String("recipient_id", req.RecipientID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send contact request"})
		return
	}

	// Send notification to recipient
	if err := s.sendContactRequestNotification(c.Request.Context(), req.RecipientID, userID.(string)); err != nil {
		s.logger.Warn("Failed to send contact request notification",
			zap.String("requester_id", userID.(string)),
			zap.String("recipient_id", req.RecipientID),
			zap.Error(err))
		// Don't fail the request if notification fails
	}

	s.logger.Info("Contact request sent successfully",
		zap.String("requester_id", userID.(string)),
		zap.String("recipient_id", req.RecipientID))

	// Return the created contact request with real user data
	convertedContact := s.convertDatabaseContact(contact)
	contactResponse, err := s.buildContactResponse(c.Request.Context(), convertedContact)
	if err != nil {
		s.logger.Error("Failed to build contact response", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create contact response"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Contact request sent successfully",
		"request": contactResponse,
	})
}

// acceptContactRequest handles accepting a contact request
func (s *Service) acceptContactRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("id")

	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Request ID is required"})
		return
	}

	// Get the contact request
	contact, err := s.getContactByID(c.Request.Context(), requestID)
	if err != nil {
		s.logger.Error("Failed to get contact request", 
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact request not found"})
		return
	}

	// Verify user is the recipient
	if contact.RecipientID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only accept requests sent to you"})
		return
	}

	// Verify request is pending
	if contact.Status != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Contact request is not pending"})
		return
	}

	// Update contact status to accepted
	now := time.Now()
	contact.Status = "accepted"
	contact.AcceptedAt = &now

	if err := s.updateContact(c.Request.Context(), contact); err != nil {
		s.logger.Error("Failed to accept contact request",
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to accept contact request"})
		return
	}

	// Send notification to requester about acceptance
	if err := s.sendContactRequestAcceptedNotification(c.Request.Context(), contact.RequesterID, contact.RecipientID); err != nil {
		s.logger.Warn("Failed to send contact request accepted notification",
			zap.String("requester_id", contact.RequesterID),
			zap.String("recipient_id", contact.RecipientID),
			zap.Error(err))
	}



	s.logger.Info("Contact request accepted successfully",
		zap.String("request_id", requestID),
		zap.String("requester_id", contact.RequesterID),
		zap.String("recipient_id", contact.RecipientID))

	c.JSON(http.StatusOK, gin.H{"message": "Contact request accepted successfully"})
}

// declineContactRequest handles declining a contact request
func (s *Service) declineContactRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("id")

	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Request ID is required"})
		return
	}

	// Get the contact request
	contact, err := s.getContactByID(c.Request.Context(), requestID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact request not found"})
		return
	}

	// Verify user is the recipient
	if contact.RecipientID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only decline requests sent to you"})
		return
	}

	// Verify request is pending
	if contact.Status != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Contact request is not pending"})
		return
	}

	// Update contact status to declined
	contact.Status = "declined"

	if err := s.updateContact(c.Request.Context(), contact); err != nil {
		s.logger.Error("Failed to decline contact request",
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decline contact request"})
		return
	}

	// Send notification to requester about decline
	if err := s.sendContactRequestDeclinedNotification(c.Request.Context(), contact.RequesterID, contact.RecipientID); err != nil {
		s.logger.Warn("Failed to send contact request declined notification",
			zap.String("requester_id", contact.RequesterID),
			zap.String("recipient_id", contact.RecipientID),
			zap.Error(err))
	}

	s.logger.Info("Contact request declined successfully",
		zap.String("request_id", requestID),
		zap.String("requester_id", contact.RequesterID),
		zap.String("recipient_id", contact.RecipientID))

	c.JSON(http.StatusOK, gin.H{"message": "Contact request declined successfully"})
}

// getContacts handles getting user's accepted contacts
func (s *Service) getContacts(c *gin.Context) {
	userID, _ := c.Get("user_id")

	contacts, err := s.arangoDB.GetContactsByUser(c.Request.Context(), userID.(string))
	if err != nil {
		s.logger.Error("Failed to get contacts", 
			zap.String("user_id", userID.(string)),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get contacts"})
		return
	}

	// Convert to response format
	contactResponses := make([]*ContactResponse, len(contacts))
	for i, contact := range contacts {
		contactResponses[i] = &ContactResponse{
			ID:         contact.Key,
			SenderId:   contact.RequesterID,
			ReceiverId: contact.RecipientID,
			Status:     contact.Status,
			SentAt:     contact.CreatedAt,
			RespondedAt: contact.AcceptedAt,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"contacts": contactResponses,
		"count":    len(contactResponses),
	})
}

// getSentRequests handles getting user's sent contact requests
func (s *Service) getSentRequests(c *gin.Context) {
	userID, _ := c.Get("user_id")

	contacts, err := s.getSentContactRequests(c.Request.Context(), userID.(string))
	if err != nil {
		s.logger.Error("Failed to get sent requests", 
			zap.String("user_id", userID.(string)),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get sent requests"})
		return
	}

	// Convert to response format with real user data
	requestResponses := make([]*ContactResponse, len(contacts))
	for i, contact := range contacts {
		response, err := s.buildContactResponse(c.Request.Context(), contact)
		if err != nil {
			s.logger.Error("Failed to build contact response",
				zap.String("contact_id", contact.Key),
				zap.Error(err))
			// Continue with next contact instead of failing the entire request
			continue
		}
		requestResponses[i] = response
	}

	c.JSON(http.StatusOK, gin.H{
		"requests": requestResponses,
		"count":    len(requestResponses),
	})
}

// getReceivedRequests handles getting user's received contact requests
func (s *Service) getReceivedRequests(c *gin.Context) {
	userID, _ := c.Get("user_id")

	contacts, err := s.getReceivedContactRequests(c.Request.Context(), userID.(string))
	if err != nil {
		s.logger.Error("Failed to get received requests", 
			zap.String("user_id", userID.(string)),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get received requests"})
		return
	}

	// Convert to response format with real user data
	requestResponses := make([]*ContactResponse, len(contacts))
	for i, contact := range contacts {
		response, err := s.buildContactResponse(c.Request.Context(), contact)
		if err != nil {
			s.logger.Error("Failed to build contact response",
				zap.String("contact_id", contact.Key),
				zap.Error(err))
			// Continue with next contact instead of failing the entire request
			continue
		}
		requestResponses[i] = response
	}

	c.JSON(http.StatusOK, gin.H{
		"requests": requestResponses,
		"count":    len(requestResponses),
	})
}

// removeContact handles removing a contact relationship
func (s *Service) removeContact(c *gin.Context) {
	userID, _ := c.Get("user_id")
	contactID := c.Param("contactId")

	if contactID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Contact ID is required"})
		return
	}

	// Get the contact to verify ownership
	contact, err := s.getContactByID(c.Request.Context(), contactID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact not found"})
		return
	}

	// Verify user is part of this contact relationship
	if contact.RequesterID != userID.(string) && contact.RecipientID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Cannot remove contact relationship you're not part of"})
		return
	}

	// Remove the contact
	if err := s.deleteContact(c.Request.Context(), contactID); err != nil {
		s.logger.Error("Failed to remove contact", 
			zap.String("contact_id", contactID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove contact"})
		return
	}

	s.logger.Info("Contact removed successfully", 
		zap.String("contact_id", contactID),
		zap.String("user_id", userID.(string)))

	c.JSON(http.StatusOK, gin.H{"message": "Contact removed successfully"})
}

// getContactRequest handles getting a specific contact request
func (s *Service) getContactRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("id")

	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Request ID is required"})
		return
	}

	contact, err := s.getContactByID(c.Request.Context(), requestID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact request not found"})
		return
	}

	// Verify user is involved in this request
	if contact.RequesterID != userID.(string) && contact.RecipientID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	response := &ContactResponse{
		ID:              contact.Key,
		SenderId:        contact.RequesterID,
		ReceiverId:      contact.RecipientID,
		Status:          contact.Status,
		SentAt:          contact.CreatedAt,
		RespondedAt:     contact.AcceptedAt,
	}

	c.JSON(http.StatusOK, gin.H{"request": response})
}

// getContactRequestHistory handles getting contact request history
func (s *Service) getContactRequestHistory(c *gin.Context) {
	userID, _ := c.Get("user_id")

	query := `
		FOR contact IN contacts
		FILTER contact.requester_id == @userID OR contact.recipient_id == @userID
		SORT contact.created_at DESC
		RETURN contact`

	cursor, err := s.arangoDB.Database.Query(c.Request.Context(), query, map[string]interface{}{
		"userID": userID.(string),
	})
	if err != nil {
		s.logger.Error("Failed to get contact request history",
			zap.String("user_id", userID.(string)),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get request history"})
		return
	}
	defer cursor.Close()

	var contacts []*Contact
	for cursor.HasMore() {
		var contact Contact
		_, err := cursor.ReadDocument(c.Request.Context(), &contact)
		if err != nil {
			continue
		}
		contacts = append(contacts, &contact)
	}

	// Convert to response format
	requestResponses := make([]*ContactResponse, len(contacts))
	for i, contact := range contacts {
		requestResponses[i] = &ContactResponse{
			ID:              contact.Key,
			SenderId:        contact.RequesterID,
			ReceiverId:      contact.RecipientID,
			Status:          contact.Status,
			SentAt:          contact.CreatedAt,
			RespondedAt:     contact.AcceptedAt,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"requests": requestResponses,
		"count":    len(requestResponses),
	})
}

// expireOldRequests handles expiring old contact requests
func (s *Service) expireOldRequests(c *gin.Context) {
	// Expire requests older than 30 days
	query := `
		FOR contact IN contacts
		FILTER contact.status == "pending"
		AND DATE_DIFF(contact.created_at, DATE_NOW(), "day") > 30
		UPDATE contact WITH { status: "expired", updated_at: DATE_NOW() } IN contacts
		RETURN NEW`

	cursor, err := s.arangoDB.Database.Query(c.Request.Context(), query, nil)
	if err != nil {
		s.logger.Error("Failed to expire old requests", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to expire old requests"})
		return
	}
	defer cursor.Close()

	var expiredCount int
	for cursor.HasMore() {
		var contact Contact
		_, err := cursor.ReadDocument(c.Request.Context(), &contact)
		if err == nil {
			expiredCount++
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Old requests expired successfully",
		"expired_count": expiredCount,
	})
}

// cancelContactRequest handles canceling a contact request
func (s *Service) cancelContactRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("id")

	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Request ID is required"})
		return
	}

	// Get the contact request
	contact, err := s.getContactByID(c.Request.Context(), requestID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact request not found"})
		return
	}

	// Verify user is the requester
	if contact.RequesterID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only cancel your own requests"})
		return
	}

	// Verify request is still pending
	if contact.Status != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Can only cancel pending requests"})
		return
	}

	// Update status to cancelled
	contact.Status = "cancelled"

	if err := s.updateContact(c.Request.Context(), contact); err != nil {
		s.logger.Error("Failed to cancel contact request",
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel request"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Contact request cancelled successfully"})
}

// getMutualContacts handles getting mutual contacts between users
func (s *Service) getMutualContacts(c *gin.Context) {
	userID, _ := c.Get("user_id")
	targetUserID := c.Query("userId")

	if targetUserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Target user ID is required"})
		return
	}

	// Get contacts for both users and find mutual ones
	query := `
		LET user1Contacts = (
			FOR contact IN contacts
			FILTER contact.requester_id == @userID AND contact.status == "accepted"
			RETURN contact.recipient_id
		)
		LET user2Contacts = (
			FOR contact IN contacts
			FILTER contact.requester_id == @targetUserID AND contact.status == "accepted"
			RETURN contact.recipient_id
		)
		LET mutualContactIds = INTERSECTION(user1Contacts, user2Contacts)
		FOR userId IN mutualContactIds
			FOR user IN users
			FILTER user._key == userId
			RETURN {
				id: user._key,
				username: user.username,
				first_name: user.first_name,
				last_name: user.last_name,
				display_name: user.display_name
			}`

	cursor, err := s.arangoDB.Database.Query(c.Request.Context(), query, map[string]interface{}{
		"userID": userID.(string),
		"targetUserID": targetUserID,
	})
	if err != nil {
		s.logger.Error("Failed to get mutual contacts", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get mutual contacts"})
		return
	}
	defer cursor.Close()

	var contacts []map[string]interface{}
	for cursor.HasMore() {
		var contact map[string]interface{}
		_, err := cursor.ReadDocument(c.Request.Context(), &contact)
		if err == nil {
			contacts = append(contacts, contact)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"contacts": contacts,
		"count": len(contacts),
	})
}

// getContactSuggestions handles getting contact suggestions
func (s *Service) getContactSuggestions(c *gin.Context) {
	userID, _ := c.Get("user_id")

	// Simple suggestion logic: users who are contacts of your contacts but not your contacts
	query := `
		LET userContacts = (
			FOR contact IN contacts
			FILTER (contact.requester_id == @userID OR contact.recipient_id == @userID)
			AND contact.status == "accepted"
			RETURN contact.requester_id == @userID ? contact.recipient_id : contact.requester_id
		)
		LET suggestions = (
			FOR contactId IN userContacts
				FOR contact IN contacts
				FILTER (contact.requester_id == contactId OR contact.recipient_id == contactId)
				AND contact.status == "accepted"
				LET suggestedId = contact.requester_id == contactId ? contact.recipient_id : contact.requester_id
				FILTER suggestedId != @userID AND suggestedId NOT IN userContacts
				RETURN DISTINCT suggestedId
		)
		FOR userId IN suggestions
			FOR user IN users
			FILTER user._key == userId
			LIMIT 10
			RETURN {
				id: user._key,
				username: user.username,
				first_name: user.first_name,
				last_name: user.last_name,
				display_name: user.display_name
			}`

	cursor, err := s.arangoDB.Database.Query(c.Request.Context(), query, map[string]interface{}{
		"userID": userID.(string),
	})
	if err != nil {
		s.logger.Error("Failed to get contact suggestions", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get contact suggestions"})
		return
	}
	defer cursor.Close()

	var suggestions []map[string]interface{}
	for cursor.HasMore() {
		var suggestion map[string]interface{}
		_, err := cursor.ReadDocument(c.Request.Context(), &suggestion)
		if err == nil {
			suggestions = append(suggestions, suggestion)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"suggestions": suggestions,
		"count": len(suggestions),
	})
}

// Helper methods

// contactRelationshipExists checks if a contact relationship already exists
func (s *Service) contactRelationshipExists(ctx context.Context, userID1, userID2 string) (bool, error) {
	query := `
		FOR contact IN contacts
		FILTER (
			(contact.requester_id == @userID1 AND contact.recipient_id == @userID2) OR
			(contact.requester_id == @userID2 AND contact.recipient_id == @userID1)
		)
		AND contact.status IN ["pending", "accepted"]
		LIMIT 1
		RETURN contact`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"userID1": userID1,
		"userID2": userID2,
	})
	if err != nil {
		return false, err
	}
	defer cursor.Close()

	return cursor.HasMore(), nil
}

// getContactByID retrieves a contact by ID
func (s *Service) getContactByID(ctx context.Context, contactID string) (*Contact, error) {
	query := `
		FOR contact IN contacts
		FILTER contact._key == @contactID
		RETURN contact`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"contactID": contactID,
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close()

	if !cursor.HasMore() {
		return nil, fmt.Errorf("contact not found")
	}

	var contact Contact
	_, err = cursor.ReadDocument(ctx, &contact)
	if err != nil {
		return nil, err
	}

	return &contact, nil
}

// updateContact updates a contact in the database
func (s *Service) updateContact(ctx context.Context, contact *Contact) error {
	collection, err := s.arangoDB.Database.Collection(ctx, "contacts")
	if err != nil {
		return err
	}

	_, err = collection.UpdateDocument(ctx, contact.Key, contact)
	return err
}

// getSentContactRequests retrieves pending contact requests sent by a user
func (s *Service) getSentContactRequests(ctx context.Context, userID string) ([]*Contact, error) {
	query := `
		FOR contact IN contacts
		FILTER contact.requester_id == @userID
		AND contact.status == "pending"
		SORT contact.created_at DESC
		RETURN contact`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"userID": userID,
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close()

	var contacts []*Contact
	for cursor.HasMore() {
		var contact Contact
		_, err := cursor.ReadDocument(ctx, &contact)
		if err != nil {
			continue
		}
		contacts = append(contacts, &contact)
	}

	return contacts, nil
}

// getReceivedContactRequests retrieves contact requests received by a user
func (s *Service) getReceivedContactRequests(ctx context.Context, userID string) ([]*Contact, error) {
	query := `
		FOR contact IN contacts
		FILTER contact.recipient_id == @userID
		AND contact.status == "pending"
		SORT contact.created_at DESC
		RETURN contact`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"userID": userID,
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close()

	var contacts []*Contact
	for cursor.HasMore() {
		var contact Contact
		_, err := cursor.ReadDocument(ctx, &contact)
		if err != nil {
			continue
		}
		contacts = append(contacts, &contact)
	}

	return contacts, nil
}

// deleteContact removes a contact from the database
func (s *Service) deleteContact(ctx context.Context, contactID string) error {
	collection, err := s.arangoDB.Database.Collection(ctx, "contacts")
	if err != nil {
		return err
	}

	_, err = collection.RemoveDocument(ctx, contactID)
	return err
}

// authMiddleware provides authentication middleware
func (s *Service) authMiddleware() gin.HandlerFunc {
	return authmw.NewAuthMiddleware(s.oryClient, s.logger)
}

// fetchUserData fetches user information from the user service
func (s *Service) fetchUserData(ctx context.Context, userID string) (*UserData, error) {
	// Make HTTP request to user service
	url := fmt.Sprintf("http://localhost:4000/api/v1/users/%s", userID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch user data: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("user service returned status %d", resp.StatusCode)
	}

	var response struct {
		User UserData `json:"user"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &response.User, nil
}

// UserData represents user information from the user service
type UserData struct {
	ID        string  `json:"id"`
	Username  string  `json:"username"`
	Email     string  `json:"email"`
	FirstName string  `json:"first_name"`
	LastName  string  `json:"last_name"`
	AvatarURL *string `json:"avatar_url"`
}

// convertDatabaseContact converts a database.Contact to a Contact
func (s *Service) convertDatabaseContact(dbContact *database.Contact) *Contact {
	return &Contact{
		Key:         dbContact.Key,
		RequesterID: dbContact.RequesterID,
		RecipientID: dbContact.RecipientID,
		Status:      dbContact.Status,
		Message:     dbContact.Message,
		CreatedAt:   dbContact.CreatedAt,
		AcceptedAt:  dbContact.AcceptedAt,
	}
}

// buildContactResponse creates a ContactResponse with real user data
func (s *Service) buildContactResponse(ctx context.Context, contact *Contact) (*ContactResponse, error) {
	// Fetch sender data
	senderData, err := s.fetchUserData(ctx, contact.RequesterID)
	if err != nil {
		s.logger.Warn("Failed to fetch sender data",
			zap.String("sender_id", contact.RequesterID),
			zap.Error(err))
		// Fallback to basic data
		senderData = &UserData{
			ID:        contact.RequesterID,
			FirstName: "Unknown",
			LastName:  "User",
		}
	}

	// Fetch receiver data
	receiverData, err := s.fetchUserData(ctx, contact.RecipientID)
	if err != nil {
		s.logger.Warn("Failed to fetch receiver data",
			zap.String("receiver_id", contact.RecipientID),
			zap.Error(err))
		// Fallback to basic data
		receiverData = &UserData{
			ID:        contact.RecipientID,
			FirstName: "Unknown",
			LastName:  "User",
		}
	}

	return &ContactResponse{
		ID:                contact.Key,
		SenderId:          contact.RequesterID,
		SenderName:        senderData.FirstName + " " + senderData.LastName,
		SenderAvatarUrl:   senderData.AvatarURL,
		ReceiverId:        contact.RecipientID,
		ReceiverName:      receiverData.FirstName + " " + receiverData.LastName,
		ReceiverAvatarUrl: receiverData.AvatarURL,
		SentAt:            contact.CreatedAt,
		Status:            contact.Status,
		Message:           contact.Message,
		RespondedAt:       contact.AcceptedAt,
	}, nil
}

// sendContactRequestNotification sends a notification to the recipient about the contact request
func (s *Service) sendContactRequestNotification(ctx context.Context, recipientID, requesterID string) error {
	// Create notification request payload
	notificationReq := map[string]interface{}{
		"user_id": recipientID,
		"type":    "contact_request_received",
		"title":   "Contact Request",
		"message": "You have a new contact request!",
		"data": map[string]interface{}{
			"requester_id": requesterID,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(notificationReq)
	if err != nil {
		return fmt.Errorf("failed to marshal notification request: %w", err)
	}

	// Send HTTP request to notification service
	notificationURL := fmt.Sprintf("http://localhost:4000/api/v1/notifications")
	req, err := http.NewRequestWithContext(ctx, "POST", notificationURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create notification request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send notification request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("notification service returned status %d", resp.StatusCode)
	}

	s.logger.Info("Contact request notification sent successfully",
		zap.String("recipient_id", recipientID),
		zap.String("requester_id", requesterID))

	return nil
}

// sendContactRequestAcceptedNotification sends a notification when a contact request is accepted
func (s *Service) sendContactRequestAcceptedNotification(ctx context.Context, requesterID, acceptedByUserID string) error {
	notificationReq := map[string]interface{}{
		"user_id": requesterID,
		"type":    "contact_request_accepted",
		"title":   "Contact Request Accepted",
		"message": "Your contact request was accepted!",
		"data": map[string]interface{}{
			"accepted_by_user_id": acceptedByUserID,
		},
	}

	jsonData, err := json.Marshal(notificationReq)
	if err != nil {
		return fmt.Errorf("failed to marshal notification request: %w", err)
	}

	notificationURL := fmt.Sprintf("http://localhost:4000/api/v1/notifications")
	req, err := http.NewRequestWithContext(ctx, "POST", notificationURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create notification request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send notification request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("notification service returned status %d", resp.StatusCode)
	}

	return nil
}

// sendContactRequestDeclinedNotification sends a notification when a contact request is declined
func (s *Service) sendContactRequestDeclinedNotification(ctx context.Context, requesterID, declinedByUserID string) error {
	notificationReq := map[string]interface{}{
		"user_id": requesterID,
		"type":    "contact_request_declined",
		"title":   "Contact Request Declined",
		"message": "Your contact request was declined.",
		"data": map[string]interface{}{
			"declined_by_user_id": declinedByUserID,
		},
	}

	jsonData, err := json.Marshal(notificationReq)
	if err != nil {
		return fmt.Errorf("failed to marshal notification request: %w", err)
	}

	notificationURL := fmt.Sprintf("http://localhost:4000/api/v1/notifications")
	req, err := http.NewRequestWithContext(ctx, "POST", notificationURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create notification request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send notification request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("notification service returned status %d", resp.StatusCode)
	}

	return nil
}


