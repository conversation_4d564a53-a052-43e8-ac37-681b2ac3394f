package friendship

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	authmw "hopenbackend/pkg/middleware"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
)

// Service handles friendship operations using ArangoDB
type Service struct {
	logger      *zap.Logger
	arangoDB    *database.ArangoDBClient
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	nats        *nats.Conn
	oryClient   *ory.Client
}

// Dependencies holds the dependencies for the friendship service
type Dependencies struct {
	Logger      *zap.Logger
	ArangoDB    *database.ArangoDBClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	NATS        *nats.Conn
	OryClient   *ory.Client
}

// New creates a new friendship service instance
func New(deps *Dependencies) *Service {
	service := &Service{
		logger:      deps.Logger,
		arangoDB:    deps.ArangoDB,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		nats:        deps.NATS,
		oryClient:   deps.OryClient,
	}

	// Subscribe to bubble expiry events
	service.subscribeToBubbleEvents()

	return service
}

// RegisterRoutes registers the friendship service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	router.GET("/requests", s.authMiddleware(), s.getFriendRequests)
	router.POST("/requests/:id/accept", s.authMiddleware(), s.acceptFriendRequest)
	router.POST("/requests/:id/decline", s.authMiddleware(), s.declineFriendRequest)
	router.GET("/friends", s.authMiddleware(), s.getFriends)
	router.DELETE("/:friendId", s.authMiddleware(), s.removeFriendship)
}

// Friendship represents a friendship between two users
type Friendship struct {
	Key            string    `json:"_key,omitempty"`
	User1ID        string    `json:"user1_id"`
	User2ID        string    `json:"user2_id"`
	CreatedAt      time.Time `json:"created_at"`
	SourceBubbleID string    `json:"source_bubble_id"` // bubble that expired to create friendship
}

// FriendRequest represents an auto-generated friend request
type FriendRequest struct {
	Key            string    `json:"_key,omitempty"`
	RequesterID    string    `json:"requester_id"`
	RecipientID    string    `json:"recipient_id"`
	Status         string    `json:"status"` // pending, accepted, declined
	CreatedAt      time.Time `json:"created_at"`
	SourceBubbleID string    `json:"source_bubble_id"` // bubble expiry that triggered this
	AutoGenerated  bool      `json:"auto_generated"`   // always true - no manual friend requests
}

// BubbleExpiredEvent represents a bubble expiry event
type BubbleExpiredEvent struct {
	BubbleID  string   `json:"bubble_id"`
	Members   []string `json:"members"`
	ExpiredAt int64    `json:"expired_at"`
}

// FriendResponse represents a friend in API responses
type FriendResponse struct {
	ID             string    `json:"id"`
	UserID         string    `json:"user_id"`
	CreatedAt      time.Time `json:"created_at"`
	SourceBubbleID string    `json:"source_bubble_id"`
}

// FriendRequestResponse represents a friend request in API responses
type FriendRequestResponse struct {
	ID             string    `json:"id"`
	RequesterID    string    `json:"requester_id"`
	RecipientID    string    `json:"recipient_id"`
	Status         string    `json:"status"`
	CreatedAt      time.Time `json:"created_at"`
	SourceBubbleID string    `json:"source_bubble_id"`
	AutoGenerated  bool      `json:"auto_generated"`
}

// getFriendRequests handles getting auto-generated friend requests
func (s *Service) getFriendRequests(c *gin.Context) {
	userID, _ := c.Get("user_id")

	requests, err := s.getFriendRequestsForUser(c.Request.Context(), userID.(string))
	if err != nil {
		s.logger.Error("Failed to get friend requests",
			zap.String("user_id", userID.(string)),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get friend requests"})
		return
	}

	// Convert to response format
	requestResponses := make([]*FriendRequestResponse, len(requests))
	for i, request := range requests {
		requestResponses[i] = &FriendRequestResponse{
			ID:             request.Key,
			RequesterID:    request.RequesterID,
			RecipientID:    request.RecipientID,
			Status:         request.Status,
			CreatedAt:      request.CreatedAt,
			SourceBubbleID: request.SourceBubbleID,
			AutoGenerated:  request.AutoGenerated,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"requests": requestResponses,
		"count":    len(requestResponses),
	})
}

// acceptFriendRequest handles accepting an auto-generated friend request
func (s *Service) acceptFriendRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("id")

	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Request ID is required"})
		return
	}

	// Rate limiting for friend request acceptance
	allowed, err := s.rateLimiter.AllowSocialOperation(c.Request.Context(), userID.(string), "friend_request_accept")
	if err != nil {
		s.logger.Error("Rate limit check failed", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if !allowed {
		c.JSON(http.StatusTooManyRequests, gin.H{"error": "Friend request acceptance rate limit exceeded"})
		return
	}

	// Get the friend request
	request, err := s.getFriendRequestByID(c.Request.Context(), requestID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Friend request not found"})
		return
	}

	// Verify user is the recipient
	if request.RecipientID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only accept requests sent to you"})
		return
	}

	// Verify request is pending
	if request.Status != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Friend request is not pending"})
		return
	}

	// Check if both users have accepted (bidirectional requirement)
	reciprocalRequest, err := s.getReciprocalFriendRequest(c.Request.Context(), request.RequesterID, request.RecipientID, request.SourceBubbleID)
	if err != nil {
		s.logger.Error("Failed to get reciprocal friend request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	// Update current request status
	request.Status = "accepted"
	if err := s.updateFriendRequest(c.Request.Context(), request); err != nil {
		s.logger.Error("Failed to update friend request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to accept friend request"})
		return
	}

	// Check if reciprocal request is also accepted
	if reciprocalRequest != nil && reciprocalRequest.Status == "accepted" {
		// Both users have accepted - create friendship
		friendship := &Friendship{
			User1ID:        request.RequesterID,
			User2ID:        request.RecipientID,
			CreatedAt:      time.Now(),
			SourceBubbleID: request.SourceBubbleID,
		}

		if err := s.createFriendship(c.Request.Context(), friendship); err != nil {
			s.logger.Error("Failed to create friendship", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create friendship"})
			return
		}

		s.logger.Info("Friendship created successfully",
			zap.String("user1_id", request.RequesterID),
			zap.String("user2_id", request.RecipientID),
			zap.String("source_bubble_id", request.SourceBubbleID))

		c.JSON(http.StatusOK, gin.H{
			"message":            "Friend request accepted and friendship created",
			"friendship_created": true,
		})
	} else {
		s.logger.Info("Friend request accepted, waiting for reciprocal acceptance",
			zap.String("request_id", requestID),
			zap.String("requester_id", request.RequesterID),
			zap.String("recipient_id", request.RecipientID))

		c.JSON(http.StatusOK, gin.H{
			"message":            "Friend request accepted, waiting for reciprocal acceptance",
			"friendship_created": false,
		})
	}
}

// declineFriendRequest handles declining an auto-generated friend request
func (s *Service) declineFriendRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("id")

	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Request ID is required"})
		return
	}

	// Get the friend request
	request, err := s.getFriendRequestByID(c.Request.Context(), requestID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Friend request not found"})
		return
	}

	// Verify user is the recipient
	if request.RecipientID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only decline requests sent to you"})
		return
	}

	// Verify request is pending
	if request.Status != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Friend request is not pending"})
		return
	}

	// Update request status to declined
	request.Status = "declined"
	if err := s.updateFriendRequest(c.Request.Context(), request); err != nil {
		s.logger.Error("Failed to decline friend request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decline friend request"})
		return
	}

	s.logger.Info("Friend request declined successfully",
		zap.String("request_id", requestID),
		zap.String("requester_id", request.RequesterID),
		zap.String("recipient_id", request.RecipientID))

	c.JSON(http.StatusOK, gin.H{"message": "Friend request declined successfully"})
}

// getFriends handles getting user's friends
func (s *Service) getFriends(c *gin.Context) {
	userID, _ := c.Get("user_id")

	friends, err := s.getFriendsForUser(c.Request.Context(), userID.(string))
	if err != nil {
		s.logger.Error("Failed to get friends",
			zap.String("user_id", userID.(string)),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get friends"})
		return
	}

	// Convert to response format
	friendResponses := make([]*FriendResponse, len(friends))
	for i, friendship := range friends {
		// Determine the other user ID
		otherUserID := friendship.User2ID
		if friendship.User2ID == userID.(string) {
			otherUserID = friendship.User1ID
		}

		friendResponses[i] = &FriendResponse{
			ID:             friendship.Key,
			UserID:         otherUserID,
			CreatedAt:      friendship.CreatedAt,
			SourceBubbleID: friendship.SourceBubbleID,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"friends": friendResponses,
		"count":   len(friendResponses),
	})
}

// removeFriendship handles removing a friendship
func (s *Service) removeFriendship(c *gin.Context) {
	userID, _ := c.Get("user_id")
	friendshipID := c.Param("friendId")

	if friendshipID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Friendship ID is required"})
		return
	}

	// Get the friendship to verify ownership
	friendship, err := s.getFriendshipByID(c.Request.Context(), friendshipID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Friendship not found"})
		return
	}

	// Verify user is part of this friendship
	if friendship.User1ID != userID.(string) && friendship.User2ID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Cannot remove friendship you're not part of"})
		return
	}

	// Remove the friendship
	if err := s.deleteFriendship(c.Request.Context(), friendshipID); err != nil {
		s.logger.Error("Failed to remove friendship",
			zap.String("friendship_id", friendshipID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove friendship"})
		return
	}

	s.logger.Info("Friendship removed successfully",
		zap.String("friendship_id", friendshipID),
		zap.String("user_id", userID.(string)))

	c.JSON(http.StatusOK, gin.H{"message": "Friendship removed successfully"})
}

// subscribeToBubbleEvents subscribes to bubble expiry events using JetStream
func (s *Service) subscribeToBubbleEvents() {
	if s.nats == nil {
		s.logger.Warn("NATS connection not available, cannot subscribe to bubble events")
		return
	}

	// Initialize JetStream context
	js, err := s.nats.JetStream()
	if err != nil {
		s.logger.Error("Failed to create JetStream context", zap.Error(err))
		return
	}

	// Create durable consumer for friendship generation
	consumerConfig := &nats.ConsumerConfig{
		Durable:       "friendship-generator-consumer",
		AckPolicy:     nats.AckExplicitPolicy,
		MaxDeliver:    3,
		AckWait:       30 * time.Second,
		ReplayPolicy:  nats.ReplayInstantPolicy,
		FilterSubject: "events.bubble.expired",
	}

	// Create or update consumer
	_, err = js.AddConsumer("BUBBLE_EVENTS", consumerConfig)
	if err != nil {
		s.logger.Error("Failed to create friendship generator consumer", zap.Error(err))
		return
	}

	// Subscribe with JetStream for reliable processing
	sub, err := js.PullSubscribe("events.bubble.expired", "friendship-generator-consumer")
	if err != nil {
		s.logger.Error("Failed to subscribe to bubble expiry events", zap.Error(err))
		return
	}

	s.logger.Info("Subscribed to bubble expiry events with JetStream",
		zap.String("consumer", "friendship-generator-consumer"))

	// Start processing messages
	go s.processBubbleExpiryMessages(sub)
}

// processBubbleExpiryMessages processes bubble expiry messages with JetStream
func (s *Service) processBubbleExpiryMessages(sub *nats.Subscription) {
	for {
		// Fetch messages in batches
		msgs, err := sub.Fetch(10, nats.MaxWait(5*time.Second))
		if err != nil {
			if err == nats.ErrTimeout {
				continue // No messages available
			}
			s.logger.Error("Failed to fetch bubble expiry messages", zap.Error(err))
			time.Sleep(5 * time.Second)
			continue
		}

		// Process each message
		for _, msg := range msgs {
			if err := s.handleBubbleExpiryMessage(msg); err != nil {
				s.logger.Error("Failed to process bubble expiry message", zap.Error(err))
				if err := msg.Nak(); err != nil {
					s.logger.Error("Failed to NAK message", zap.Error(err))
				}
			} else {
				if err := msg.Ack(); err != nil {
					s.logger.Error("Failed to ACK message", zap.Error(err))
				}
			}
		}
	}
}

// handleBubbleExpiryMessage handles a single bubble expiry message
func (s *Service) handleBubbleExpiryMessage(msg *nats.Msg) error {
	var event BubbleExpiredEvent
	if err := json.Unmarshal(msg.Data, &event); err != nil {
		return fmt.Errorf("failed to unmarshal bubble expiry event: %w", err)
	}

	s.logger.Info("Processing bubble expiry event",
		zap.String("bubble_id", event.BubbleID),
		zap.Strings("members", event.Members),
		zap.Int("member_count", len(event.Members)))

	// Only generate friend requests if there are 2 or more members
	if len(event.Members) < 2 {
		s.logger.Info("Bubble has less than 2 members, skipping friendship generation",
			zap.String("bubble_id", event.BubbleID))
		return nil
	}

	// Get bubble information for notifications
	ctx := context.Background()
	var bubbleName string
	err := s.db.Pool.QueryRow(ctx, `SELECT name FROM bubbles WHERE id = $1`, event.BubbleID).Scan(&bubbleName)
	if err != nil {
		s.logger.Warn("Failed to get bubble name for notifications", zap.String("bubble_id", event.BubbleID), zap.Error(err))
		bubbleName = "Unknown Bubble"
	}

	// Get member information for the friend selection dialog
	formerMembersData := make([]map[string]interface{}, 0, len(event.Members))
	for _, memberID := range event.Members {
		// Get user info for each member
		if userInfo, err := s.getUserInfo(memberID); err == nil {
			memberData := map[string]interface{}{
				"id":                 memberID,
				"user_id":            memberID,
				"username":           userInfo.Username,
				"firstName":          userInfo.FirstName,
				"first_name":         userInfo.FirstName,
				"lastName":           userInfo.LastName,
				"last_name":          userInfo.LastName,
				"profilePictureUrl":  userInfo.AvatarUrl,
				"profile_picture_url": userInfo.AvatarUrl,
				"isOnline":           false, // Default to offline
				"is_online":          false,
			}
			formerMembersData = append(formerMembersData, memberData)
		}
	}

	// Send bubble expired notifications to all former members to trigger friend selection dialog
	for _, memberID := range event.Members {
		if err := s.notificationService.SendBubbleExpiredNotification(ctx, memberID, event.BubbleID, bubbleName, formerMembersData); err != nil {
			s.logger.Warn("Failed to send bubble expired notification",
				zap.String("member_id", memberID),
				zap.String("bubble_id", event.BubbleID),
				zap.Error(err))
		}
	}

	// Generate friend requests for all member pairs
	friendRequestsCreated := 0
	for i := 0; i < len(event.Members); i++ {
		for j := i + 1; j < len(event.Members); j++ {
			user1ID := event.Members[i]
			user2ID := event.Members[j]

			// Create bidirectional friend requests
			if err := s.createAutoFriendRequest(user1ID, user2ID, event.BubbleID); err != nil {
				s.logger.Error("Failed to create friend request",
					zap.String("requester", user1ID),
					zap.String("recipient", user2ID),
					zap.String("bubble_id", event.BubbleID),
					zap.Error(err))
			} else {
				friendRequestsCreated++
			}

			if err := s.createAutoFriendRequest(user2ID, user1ID, event.BubbleID); err != nil {
				s.logger.Error("Failed to create friend request",
					zap.String("requester", user2ID),
					zap.String("recipient", user1ID),
					zap.String("bubble_id", event.BubbleID),
					zap.Error(err))
			} else {
				friendRequestsCreated++
			}
		}
	}

	s.logger.Info("Completed processing bubble expiry event",
		zap.String("bubble_id", event.BubbleID),
		zap.Int("member_count", len(event.Members)),
		zap.Int("friend_requests_created", friendRequestsCreated))

	return nil
}

// createAutoFriendRequest creates an auto-generated friend request
func (s *Service) createAutoFriendRequest(requesterID, recipientID, sourceBubbleID string) error {
	ctx := context.Background()

	// Check if friend request already exists
	exists, err := s.friendRequestExists(ctx, requesterID, recipientID, sourceBubbleID)
	if err != nil {
		return err
	}
	if exists {
		return nil // Request already exists
	}

	// Create friend request
	request := &FriendRequest{
		RequesterID:    requesterID,
		RecipientID:    recipientID,
		Status:         "pending",
		CreatedAt:      time.Now(),
		SourceBubbleID: sourceBubbleID,
		AutoGenerated:  true,
	}

	return s.createFriendRequest(ctx, request)
}

// Helper methods for database operations

// createFriendRequest creates a friend request in ArangoDB
func (s *Service) createFriendRequest(ctx context.Context, request *FriendRequest) error {
	collection, err := s.arangoDB.Database.Collection(ctx, "friend_requests")
	if err != nil {
		return err
	}

	_, err = collection.CreateDocument(ctx, request)
	return err
}

// createFriendship creates a friendship in ArangoDB
func (s *Service) createFriendship(ctx context.Context, friendship *Friendship) error {
	collection, err := s.arangoDB.Database.Collection(ctx, "friendships")
	if err != nil {
		return err
	}

	_, err = collection.CreateDocument(ctx, friendship)
	return err
}

// getFriendRequestsForUser retrieves friend requests for a user
func (s *Service) getFriendRequestsForUser(ctx context.Context, userID string) ([]*FriendRequest, error) {
	query := `
		FOR request IN friend_requests
		FILTER request.recipient_id == @userID
		AND request.status == "pending"
		SORT request.created_at DESC
		RETURN request`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"userID": userID,
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close()

	var requests []*FriendRequest
	for cursor.HasMore() {
		var request FriendRequest
		_, err := cursor.ReadDocument(ctx, &request)
		if err != nil {
			continue
		}
		requests = append(requests, &request)
	}

	return requests, nil
}

// getFriendsForUser retrieves friends for a user
func (s *Service) getFriendsForUser(ctx context.Context, userID string) ([]*Friendship, error) {
	query := `
		FOR friendship IN friendships
		FILTER friendship.user1_id == @userID OR friendship.user2_id == @userID
		SORT friendship.created_at DESC
		RETURN friendship`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"userID": userID,
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close()

	var friendships []*Friendship
	for cursor.HasMore() {
		var friendship Friendship
		_, err := cursor.ReadDocument(ctx, &friendship)
		if err != nil {
			continue
		}
		friendships = append(friendships, &friendship)
	}

	return friendships, nil
}

// getFriendRequestByID retrieves a friend request by ID
func (s *Service) getFriendRequestByID(ctx context.Context, requestID string) (*FriendRequest, error) {
	query := `
		FOR request IN friend_requests
		FILTER request._key == @requestID
		RETURN request`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"requestID": requestID,
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close()

	if !cursor.HasMore() {
		return nil, fmt.Errorf("friend request not found")
	}

	var request FriendRequest
	_, err = cursor.ReadDocument(ctx, &request)
	if err != nil {
		return nil, err
	}

	return &request, nil
}

// getReciprocalFriendRequest gets the reciprocal friend request
func (s *Service) getReciprocalFriendRequest(ctx context.Context, requesterID, recipientID, sourceBubbleID string) (*FriendRequest, error) {
	query := `
		FOR request IN friend_requests
		FILTER request.requester_id == @recipientID
		AND request.recipient_id == @requesterID
		AND request.source_bubble_id == @sourceBubbleID
		RETURN request`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"requesterID":    requesterID,
		"recipientID":    recipientID,
		"sourceBubbleID": sourceBubbleID,
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close()

	if !cursor.HasMore() {
		return nil, nil // No reciprocal request found
	}

	var request FriendRequest
	_, err = cursor.ReadDocument(ctx, &request)
	if err != nil {
		return nil, err
	}

	return &request, nil
}

// updateFriendRequest updates a friend request
func (s *Service) updateFriendRequest(ctx context.Context, request *FriendRequest) error {
	collection, err := s.arangoDB.Database.Collection(ctx, "friend_requests")
	if err != nil {
		return err
	}

	_, err = collection.UpdateDocument(ctx, request.Key, request)
	return err
}

// friendRequestExists checks if a friend request already exists
func (s *Service) friendRequestExists(ctx context.Context, requesterID, recipientID, sourceBubbleID string) (bool, error) {
	query := `
		FOR request IN friend_requests
		FILTER request.requester_id == @requesterID
		AND request.recipient_id == @recipientID
		AND request.source_bubble_id == @sourceBubbleID
		LIMIT 1
		RETURN request`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"requesterID":    requesterID,
		"recipientID":    recipientID,
		"sourceBubbleID": sourceBubbleID,
	})
	if err != nil {
		return false, err
	}
	defer cursor.Close()

	return cursor.HasMore(), nil
}

// getFriendshipByID retrieves a friendship by ID
func (s *Service) getFriendshipByID(ctx context.Context, friendshipID string) (*Friendship, error) {
	query := `
		FOR friendship IN friendships
		FILTER friendship._key == @friendshipID
		RETURN friendship`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"friendshipID": friendshipID,
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close()

	if !cursor.HasMore() {
		return nil, fmt.Errorf("friendship not found")
	}

	var friendship Friendship
	_, err = cursor.ReadDocument(ctx, &friendship)
	if err != nil {
		return nil, err
	}

	return &friendship, nil
}

// deleteFriendship removes a friendship
func (s *Service) deleteFriendship(ctx context.Context, friendshipID string) error {
	collection, err := s.arangoDB.Database.Collection(ctx, "friendships")
	if err != nil {
		return err
	}

	_, err = collection.RemoveDocument(ctx, friendshipID)
	return err
}

// authMiddleware provides authentication middleware
func (s *Service) authMiddleware() gin.HandlerFunc {
	return authmw.NewAuthMiddleware(s.oryClient, s.logger)
}

// UserInfo represents basic user information for notifications
type UserInfo struct {
	Username  string  `json:"username"`
	FirstName *string `json:"first_name"`
	LastName  *string `json:"last_name"`
	AvatarUrl *string `json:"avatar_url"`
}

// getUserInfo fetches user information for enhanced notifications
func (s *Service) getUserInfo(userID string) (*UserInfo, error) {
	ctx := context.Background()
	var userInfo UserInfo

	err := s.db.Pool.QueryRow(ctx, `
		SELECT username, first_name, last_name, avatar_url
		FROM users
		WHERE id = $1`, userID).Scan(
		&userInfo.Username,
		&userInfo.FirstName,
		&userInfo.LastName,
		&userInfo.AvatarUrl,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}

	return &userInfo, nil
}
