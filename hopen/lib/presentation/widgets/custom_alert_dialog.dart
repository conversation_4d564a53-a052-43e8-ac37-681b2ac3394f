import 'package:flutter/material.dart';

class CustomAlertDialog extends StatelessWidget {
  const CustomAlertDialog({
    required this.title,
    required this.content,
    super.key,
    this.buttonText = 'OK',
    this.onButtonPressed,
  });
  final String title;
  final String content;
  final String buttonText;
  final VoidCallback? onButtonPressed;

  @override
  Widget build(BuildContext context) => AlertDialog(
    backgroundColor: const Color(0xFF1A2B4D),
    shape: RoundedSuperellipseBorder(borderRadius: BorderRadius.circular(40)),
    title: Text(
      title,
      style: const TextStyle(
        color: Color(0xFF00FFFF),
        fontWeight: FontWeight.bold,
      ),
    ),
    content: Text(content, style: const TextStyle(color: Colors.white)),
    actions: [
      TextButton(
        onPressed: onButtonPressed ?? () => Navigator.of(context).pop(),
        child: Text(
          buttonText,
          style: const TextStyle(color: Color(0xFF00FFFF)),
        ),
      ),
    ],
  );
}
