import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;

import '../../di/injection_container_refactored.dart' as di;
import '../../statefulbusinesslogic/bloc/profile_picture/profile_picture_bloc.dart';
import '../../statefulbusinesslogic/bloc/profile_picture/profile_picture_event.dart';
import '../../statefulbusinesslogic/bloc/profile_picture/profile_picture_state.dart';
import '../../statefulbusinesslogic/core/services/profile_picture_service.dart';

/// Custom cache manager that bypasses certificate issues for development
class HopenCacheManager extends CacheManager with ImageCacheManager {
  static const key = 'hopenCachedImageData';

  static HopenCacheManager? _instance;

  factory HopenCacheManager() {
    return _instance ??= HopenCacheManager._();
  }

  HopenCacheManager._() : super(
    Config(
      key,
      stalePeriod: const Duration(days: 7), // Cache for 7 days
      maxNrOfCacheObjects: 200, // Limit cache size
      repo: JsonCacheInfoRepository(databaseName: key),
      fileService: HopenHttpFileService(), // Use our custom HTTP service
    ),
  );
}

/// Custom HTTP file service that bypasses certificate issues
class HopenHttpFileService extends HttpFileService {
  @override
  Future<FileServiceResponse> get(String url, {Map<String, String>? headers}) async {
    print('🌐 HopenHttpFileService fetching: $url');

    try {
      // Use the default HTTP file service but with our custom HTTP client
      // This ensures compatibility with the expected response types
      return await super.get(url, headers: headers);
    } catch (e) {
      print('❌ HopenHttpFileService error: $e');

      // Fallback: try with certificate bypass if the default fails
      try {
        print('🔄 Trying with certificate bypass...');

        // Create HTTP client that bypasses certificate verification
        final client = HttpClient();
        client.badCertificateCallback = (cert, host, port) => true;

        final request = await client.getUrl(Uri.parse(url));
        if (headers != null) {
          headers.forEach((key, value) {
            request.headers.add(key, value);
          });
        }

        final response = await request.close();
        final bytes = await consolidateHttpClientResponseBytes(response);

        print('✅ HopenHttpFileService certificate bypass success: ${bytes.length} bytes');

        // Create a proper StreamedResponse for HttpGetResponse
        final headersMap = <String, String>{};
        response.headers.forEach((name, values) {
          headersMap[name] = values.join(', ');
        });

        final streamedResponse = http.StreamedResponse(
          Stream.value(bytes),
          response.statusCode,
          contentLength: bytes.length,
          headers: headersMap,
        );

        return HttpGetResponse(streamedResponse);
      } catch (fallbackError) {
        print('❌ HopenHttpFileService fallback also failed: $fallbackError');
        rethrow;
      }
    }
  }
}

// HopenNetworkImageProvider removed - replaced with CachedNetworkImage + HopenCacheManager
// This provides better caching, persistence, and performance while maintaining certificate bypass

/// A reusable profile picture widget that displays a Superellipse avatar
/// with proper loading states, error handling, and fallback to initials
class ProfilePictureWidget extends StatelessWidget {
  const ProfilePictureWidget({
    super.key,
    this.imageUrl,
    this.firstName,
    this.lastName,
    this.radius = 40,
    this.onTap,
    this.showEditIcon = false,
    this.backgroundColor,
  });
  final String? imageUrl;
  final String? firstName;
  final String? lastName;
  final double radius;
  final VoidCallback? onTap;
  final bool showEditIcon;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    print('🖼️ ProfilePictureWidget.build() - imageUrl: $imageUrl, firstName: $firstName, lastName: $lastName');
    
    final initials = ProfilePictureService.generateInitials(
      firstName,
      lastName,
    );
    final avatarColor =
        backgroundColor ??
        Color(ProfilePictureService.getAvatarColorForInitials(initials));

    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          Container(
            width: radius * 2,
            height: radius * 2,
            decoration: ShapeDecoration(
              shape: RoundedSuperellipseBorder(
                borderRadius: BorderRadius.circular(40),
              ),
              color: avatarColor,
            ),
            child: _buildAvatarContent(initials, avatarColor),
          ),
          if (showEditIcon)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: ShapeDecoration(
                  shape: RoundedSuperellipseBorder(
                    borderRadius: BorderRadius.circular(40),
                    side: BorderSide(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      width: 2,
                    ),
                  ),
                  color: Theme.of(context).primaryColor,
                ),
                child: Icon(
                  Icons.camera_alt,
                  size: radius * 0.4,
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAvatarContent(String initials, Color avatarColor) {
    print('🖼️ _buildAvatarContent() - imageUrl: $imageUrl, initials: $initials');
    
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      print('🖼️ Image URL is provided: $imageUrl');
      
      if (imageUrl!.startsWith('http')) {
        print('🖼️ Using CachedNetworkImage with HopenCacheManager (bypasses certificate issues + better caching)');
        return ClipRRect(
          borderRadius: BorderRadius.circular(28),
          child: CachedNetworkImage(
            key: ValueKey(imageUrl), // Stable key to prevent unnecessary rebuilds
            imageUrl: imageUrl!,
            cacheManager: HopenCacheManager(),
            width: radius * 2,
            height: radius * 2,
            fit: BoxFit.cover,
            placeholder: (context, url) {
              print('🔄 CachedNetworkImage loading: $url');
              return _buildLoadingWidget();
            },
            errorWidget: (context, url, error) {
              print('❌ CachedNetworkImage error for $url: $error');
              return _buildInitialsWidget(initials, avatarColor);
            },
            fadeInDuration: const Duration(milliseconds: 150), // Reduced for smoother transitions
            fadeOutDuration: const Duration(milliseconds: 50),  // Reduced for smoother transitions
          ),
        );
      } else {
        print('🖼️ Using Image.file for local file path');
        // Treat as local file path
        return ClipRRect(
          borderRadius: BorderRadius.circular((radius * 2) * 0.4),
          child: Image.file(
            File(imageUrl!),
            width: radius * 2,
            height: radius * 2,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              print('❌ Local file load error: $error');
              return _buildInitialsWidget(initials, avatarColor);
            },
          ),
        );
      }
    }

    print('🖼️ No image URL, showing initials: $initials');
    return _buildInitialsWidget(initials, avatarColor);
  }

  Widget _buildLoadingWidget() => Container(
    width: radius * 2,
    height: radius * 2,
    decoration: ShapeDecoration(
      shape: RoundedSuperellipseBorder(
        borderRadius: BorderRadius.circular((radius * 2) * 0.4),
      ),
      color: Colors.grey[200],
    ),
    child: Center(
      child: SizedBox(
        width: radius * 0.6,
        height: radius * 0.6,
        child: const CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    ),
  );

  Widget _buildInitialsWidget(String initials, Color backgroundColor) =>
      Container(
        width: radius * 2,
        height: radius * 2,
        decoration: ShapeDecoration(
          shape: RoundedSuperellipseBorder(
            borderRadius: BorderRadius.circular(40),
          ),
          color: backgroundColor,
        ),
        child: Center(
          child: Text(
            initials,
            style: TextStyle(
              color: Colors.white,
              fontSize: radius * 0.6,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
}

/// Profile picture picker dialog
class ProfilePicturePickerDialog extends StatefulWidget {
  const ProfilePicturePickerDialog({
    super.key,
    required this.imageUrl,
    required this.firstName,
    required this.lastName,
  });

  final String? imageUrl;
  final String? firstName;
  final String? lastName;

  @override
  State<ProfilePicturePickerDialog> createState() => _ProfilePicturePickerDialogState();

  /// Show the profile picture picker dialog
  static Future<void> show(
    BuildContext context, {
    required String? imageUrl,
    required String? firstName,
    required String? lastName,
  }) => showDialog(
    context: context,
    barrierColor: Colors.black.withValues(alpha: 0.9), // Same as bubble_history_dialog.dart
    builder: (context) => ProfilePicturePickerDialog(
      imageUrl: imageUrl,
      firstName: firstName,
      lastName: lastName,
    ),
  );
}

class _ProfilePicturePickerDialogState extends State<ProfilePicturePickerDialog> {
  late ProfilePictureBloc _profilePictureBloc;
  String? _selectedImagePath;
  bool _isUploadingImage = false;

  @override
  void initState() {
    super.initState();
    _profilePictureBloc = di.sl<ProfilePictureBloc>();
  }

  @override
  void dispose() {
    _profilePictureBloc.close();
    super.dispose();
  }

  void _pickFromGallery() {
    _profilePictureBloc.add(const PickFromGalleryEvent());
  }

  void _takePhoto() {
    _profilePictureBloc.add(const TakePhotoEvent());
  }

  void _validateAndSave() {
    if (_selectedImagePath != null) {
      _profilePictureBloc.add(ValidateImageEvent(imagePath: _selectedImagePath!));
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final spacingHeight = screenHeight / 48;
    final fieldHeight = screenHeight / 16;
    final avatarSize = screenHeight * 0.25; // Larger size for dialog

    final initials = ProfilePictureService.generateInitials(
      widget.firstName,
      widget.lastName,
    );
    final avatarColor = Color(ProfilePictureService.getAvatarColorForInitials(initials));

    return BlocListener<ProfilePictureBloc, ProfilePictureState>(
      bloc: _profilePictureBloc,
      listener: (context, state) {
        if (state is ProfilePictureLoading) {
          setState(() {
            _isUploadingImage = true;
          });
        } else {
          setState(() {
            _isUploadingImage = false;
          });

          if (state is ProfilePictureSuccess) {
            setState(() {
              _selectedImagePath = state.result.url;
            });
          } else if (state is ProfilePictureError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      },
      child: AlertDialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
        content: Container(
          width: double.infinity,
          padding: EdgeInsets.all(spacingHeight * 1.5),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Large avatar display
              Container(
                width: avatarSize,
                height: avatarSize,
                decoration: ShapeDecoration(
                  shape: RoundedSuperellipseBorder(
                    borderRadius: BorderRadius.circular(60),
                    side: BorderSide(color: Colors.white.withValues(alpha: 0.3), width: 2),
                  ),
                  color: avatarColor,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(60),
                  child: _buildLargeAvatarContent(widget.imageUrl, initials, avatarColor, avatarSize),
                ),
              ),
              SizedBox(height: spacingHeight * 2),
              // Buttons row
              SizedBox(
                height: fieldHeight,
                child: Row(
                  children: [
                    Expanded(
                      child: _buildButton(
                        context: context,
                        label: 'Gallery',
                        icon: Icons.photo_library,
                        onPressed: _pickFromGallery,
                        fieldHeight: fieldHeight,
                        spacingHeight: spacingHeight,
                      ),
                    ),
                    SizedBox(width: spacingHeight),
                    Expanded(
                      child: _buildButton(
                        context: context,
                        label: 'Camera',
                        icon: Icons.camera_alt,
                        onPressed: _takePhoto,
                        fieldHeight: fieldHeight,
                        spacingHeight: spacingHeight,
                      ),
                    ),
                  ],
                ),
              ),
              // Modify profile picture button
              SizedBox(height: spacingHeight),
              SizedBox(
                width: double.infinity,
                height: fieldHeight,
                child: _buildModifyButton(
                  context: context,
                  onPressed: _validateAndSave,
                  fieldHeight: fieldHeight,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLargeAvatarContent(String? imageUrl, String initials, Color avatarColor, double avatarSize) {
    print('🖼️ _buildLargeAvatarContent() - imageUrl: $imageUrl, initials: $initials');
    
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      print('🖼️ Large avatar - Image URL is provided: $imageUrl');
      
      if (imageUrl!.startsWith('http')) {
        print('🖼️ Large avatar - Using CachedNetworkImage with HopenCacheManager (bypasses certificate issues + better caching)');
        return CachedNetworkImage(
          imageUrl: imageUrl!,
          cacheManager: HopenCacheManager(),
          width: avatarSize,
          height: avatarSize,
          fit: BoxFit.cover,
          placeholder: (context, url) {
            print('🔄 Large avatar - CachedNetworkImage loading: $url');
            return _buildLargeLoadingWidget(avatarSize);
          },
          errorWidget: (context, url, error) {
            print('❌ Large avatar - CachedNetworkImage error for $url: $error');
            return _buildLargeInitialsWidget(initials, avatarColor, avatarSize);
          },
          fadeInDuration: const Duration(milliseconds: 200),
          fadeOutDuration: const Duration(milliseconds: 100),
        );
      } else {
        print('🖼️ Large avatar - Using Image.file for local file path');
        // Treat as local file path
        return Image.file(
          File(imageUrl!),
          width: avatarSize,
          height: avatarSize,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            print('❌ Large avatar - Local file load error: $error');
            return _buildLargeInitialsWidget(initials, avatarColor, avatarSize);
          },
        );
      }
    }

    print('🖼️ Large avatar - No image URL, showing initials: $initials');
    return _buildLargeInitialsWidget(initials, avatarColor, avatarSize);
  }

  Widget _buildLargeLoadingWidget(double avatarSize) => Container(
    width: avatarSize,
    height: avatarSize,
    color: Colors.grey[200],
    child: Center(
      child: SizedBox(
        width: avatarSize * 0.3,
        height: avatarSize * 0.3,
        child: const CircularProgressIndicator(
          strokeWidth: 3,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    ),
  );

  Widget _buildLargeInitialsWidget(String initials, Color backgroundColor, double avatarSize) =>
      Container(
        width: avatarSize,
        height: avatarSize,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: const BorderRadius.all(Radius.circular(18)),
        ),
        child: Center(
          child: Text(
            initials,
            style: TextStyle(
              color: Colors.white,
              fontSize: avatarSize * 0.3,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );

  Widget _buildButton({
    required BuildContext context,
    required String label,
    required IconData icon,
    required VoidCallback? onPressed,
    required double fieldHeight,
    required double spacingHeight,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withValues(alpha: 0.1),
        foregroundColor: Colors.white,
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(18),
          side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
        padding: EdgeInsets.zero,
        minimumSize: Size.zero,
        maximumSize: const Size(double.infinity, double.infinity),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        fixedSize: Size.fromHeight(fieldHeight),
      ),
      icon: Icon(icon),
      label: Text(label),
    );
  }

  Widget _buildModifyButton({
    required BuildContext context,
    required VoidCallback? onPressed,
    required double fieldHeight,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue, // Same as "Next" button in signup
        foregroundColor: Colors.white,
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(18),
        ),
        padding: EdgeInsets.zero,
        minimumSize: const Size(0, 0),
        maximumSize: const Size(double.infinity, double.infinity),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: const Text(
        'Modify the profile picture',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
