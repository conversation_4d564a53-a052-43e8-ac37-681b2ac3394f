import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart'; // Import flutter_svg

// WebSocket service will be accessed through dependency injection if needed
import './bubble_history_dialog.dart'; // Import the bubble history dialog
import './notifications_count_badge.dart'; // Import the notifications count badge
import './notifications_dialog.dart'; // Import the new dialog widget
import '../../statefulbusinesslogic/bloc/notification/notification_bloc.dart';
import '../../statefulbusinesslogic/bloc/notification/notification_state.dart';

/// A wrapper widget that connects MainAppBar to NotificationBloc
/// This is the widget that should be used in pages
class MainAppBar extends StatelessWidget implements PreferredSizeWidget {
  const MainAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, state) {
        int unreadCount = 0;
        if (state is NotificationsLoaded) {
          unreadCount = state.unreadCount;
        }
        return _MainAppBarImpl(unreadNotificationsCount: unreadCount);
      },
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48);
}

/// The actual implementation of the app bar widget
///
/// Features:
/// * Responsive logo sizing based on screen width
/// * Left menu button for primary navigation
/// * Right notifications button that opens a drawer with notification count badge
/// * Transparent background to work with gradient backgrounds
/// * SafeArea integration for proper spacing on notched devices
///
/// The app bar includes:
/// * A menu button on the left that can be used for primary navigation
/// * A centered Hopen logotype that scales based on screen width
/// * A notifications button on the right that opens a drawer with recent notifications
/// * A badge showing unread notification count (capped at 99) using SF Pro font
class _MainAppBarImpl extends StatelessWidget implements PreferredSizeWidget {
  const _MainAppBarImpl({required this.unreadNotificationsCount});
  final int unreadNotificationsCount;

  @override
  Widget build(BuildContext context) {
    // Get the screen width for responsive logo sizing
    final screenWidth = MediaQuery.of(context).size.width;
    // Calculate logo height (48 is the base height, scaled up by screen width)
    final logoHeight = 64 * (screenWidth / 375); // 375 is base iPhone width

    return SafeArea(
      child: ColoredBox(
        color: Colors.transparent, // Explicitly set to transparent
        child: AppBar(
          backgroundColor: Colors.transparent, // Fully transparent background
          elevation: 0, // No elevation shadow
          scrolledUnderElevation: 0, // Prevent elevation change on scroll
          surfaceTintColor: Colors.transparent, // Prevent tint on scroll
          toolbarHeight: 64, // Reduced height for more compact spacing
          leadingWidth: 72, // More space for the leading button
          leading: Container(
            padding: const EdgeInsets.only(left: 24), // Increased padding
            child: IconButton(
              icon: SvgPicture.asset(
                'assets/icons/bubble-history.svg',
                colorFilter: const ColorFilter.mode(
                  Colors.white,
                  BlendMode.srcIn,
                ),
                width: 28,
                height: 28,
              ),
              onPressed: () {
                BubbleHistoryDialog.show(context);
              },
            ),
          ),
          title: Image.asset(
            'assets/images/hopen-logotype.png',
            height: logoHeight,
            fit: BoxFit.contain,
          ),
          centerTitle: true,
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 24), // Increased padding
              child: Stack(
                children: [
                  IconButton(
                    icon: SvgPicture.asset(
                      'assets/icons/bell.svg',
                      colorFilter: const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      ),
                      width: 28,
                      height: 28,
                    ),
                    onPressed: () {
                      NotificationsDialog.show(context);
                    },
                  ),
                  if (unreadNotificationsCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: NotificationsCountBadge(
                        count: unreadNotificationsCount,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48); // Reduced height to match standard app bar height
}
