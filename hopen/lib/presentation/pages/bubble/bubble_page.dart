import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../../statefulbusinesslogic/bloc/bubble/bubble_bloc.dart';
import '../../../statefulbusinesslogic/bloc/bubble/bubble_event.dart';
import '../../../statefulbusinesslogic/bloc/bubble/bubble_state.dart';
import '../../../statefulbusinesslogic/bloc/active_bubble/active_bubble_bloc.dart';
import '../../../statefulbusinesslogic/bloc/active_bubble/active_bubble_state.dart';
import '../../../statefulbusinesslogic/core/models/bubble_entity.dart';
import '../../../statefulbusinesslogic/core/notifiers/nav_bar_visibility_notifier.dart';
import '../../router/app_router.dart';
import '../../widgets/bubble_status_card.dart';
import '../../widgets/friends_tile.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/main_app_bar.dart';
import '../../widgets/notifications_count_badge.dart';
import '../../widgets/requests/friends_choice_dialog.dart';
import 'announcement_banner_page.dart';
import '../unified_profile_page/unified_profile_page.dart';

// Define the color palette for bubble members
const List<Color> _memberColors = [
  Color(0xFF10FFED), // Bright Cyan
  Color(0xFF64FF93), // Bright Green
  Color(0xFFC4FF2D), // Lime Green
  Color(0xFFF0FF00), // Yellow
];

class BubblePage extends StatefulWidget {
  const BubblePage({super.key});

  @override
  State<BubblePage> createState() => _BubblePageState();
}

class _BubblePageState extends State<BubblePage> {
  late NavBarVisibilityNotifier _navBarNotifier;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _navBarNotifier = Provider.of<NavBarVisibilityNotifier>(
      context,
      listen: false,
    );

    final route = ModalRoute.of(context);
    if (route?.isCurrent ?? false) {
      // Use post-frame callback to avoid calling during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _navBarNotifier.showNavBar();
        }
      });
    }

    // Trigger the LoadBubble event when dependencies change
    context.read<BubbleBloc>().add(const LoadBubble());
  }

  Future<void> _navigateToSubPage(Widget page) async {
    _navBarNotifier.hideNavBar();
    await Navigator.push<void>(
      context,
      MaterialPageRoute<void>(builder: (context) => page),
    );
    if (mounted) {
      _navBarNotifier.showNavBar();
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final tileSeparation = screenHeight / 48;

    return BlocListener<ActiveBubbleBloc, ActiveBubbleState>(
      listener: (context, state) {
        if (state is ActiveBubbleExpired) {
          // Show bubble expiration alert dialog
          WidgetsBinding.instance.addPostFrameCallback((_) {
            FriendsChoiceDialog.show(
              context,
              bubbleId: state.expiredBubble.id.value,
              bubbleName: state.expiredBubble.name.value,
              formerMembers: [], // TODO: Get former members from the expired bubble
            );
          });
        }
      },
      child: GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: const MainAppBar(),
        body: SafeArea(
          bottom: false,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Center(
                child: Padding(
                  padding: EdgeInsets.zero,
                  child: Text(
                    'Bubble',
                    style: TextStyle(
                      color: Color(0xFF00FFFF),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: BlocBuilder<BubbleBloc, BubbleState>(
                  builder: (context, state) {
                    if (state is BubbleLoading) {
                      return const Center(child: CircularProgressIndicator());
                    }
                    if (state is BubbleError) {
                      return Center(child: Text('Error: ${state.userMessage}'));
                    }
                    if (state is BubbleInitial) {
                      // User not in any bubble
                      return const Center(
                        child: Text(
                          'You are not in a bubble yet :)\nFind some contacts to start a bubble with\nor join an existing bubble',
                          style: TextStyle(color: Colors.grey),
                          textAlign: TextAlign.center,
                        ),
                      );
                    }
                    if (state is BubbleLoaded) {
                      // Check if user has no bubble members (not in a bubble)
                      if (state.bubble.members.isEmpty) {
                        return const Center(
                          child: Text(
                            'You are not in a bubble yet :)\nFind some contacts to start a bubble with\nor join an existing bubble',
                            style: TextStyle(color: Colors.grey),
                            textAlign: TextAlign.center,
                          ),
                        );
                      }

                      return SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.only(
                            left: 20,
                            right: 20,
                            bottom: kBottomNavigationBarHeight + 20.0,
                          ),
                          child: Column(
                            children: [
                              BubbleStatusCard(
                                bubble: state.bubble,
                              ),
                              // Map members to tiles with assigned colors and unread counts
                              ...state.bubble.members.asMap().entries.map((entry) {
                                final index = entry.key;
                                final member = entry.value;
                                // Get online status from the member entity
                                final isOnline = member.isOnline;
                                // Get unread count - for now use 0
                                const unreadCount = 0;
                                // Assign color based on index modulo the number of colors
                                final memberColor =
                                    _memberColors[index % _memberColors.length];
                                return _buildMemberCard(
                                  member,
                                  memberColor,
                                  isOnline,
                                  unreadCount, // Pass unread count
                                  context,
                                );
                              }),
                              SizedBox(height: screenHeight / 96),
                              // Conditionally render the propose button
                              if (state.bubble.members.length <
                                  4) // User is 5th member, so max 4 others
                                Column(
                                  // Wrap button and SizedBox together
                                  children: [
                                    _buildProposeNewMemberButton(context),
                                    SizedBox(
                                      height: tileSeparation,
                                    ), // Only show spacing if button is shown
                                  ],
                                ),
                              _buildMoreBubblesButton(context),
                            ],
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    ),
    );
  }

  Widget _buildMemberCard(
    BubbleMemberEntity member,
    Color nameColor,
    bool isOnline,
    int unreadCount, // Added unreadCount parameter
    BuildContext context,
  ) {
    // Format the joined date
    final joinedDate = member.joinedAt;
    final formattedDate = '${joinedDate.day}/${joinedDate.month}/${joinedDate.year}';

    return FriendsTile(
      name: member.name,
      subtitle: 'Bubbler since $formattedDate',
      avatarUrl: member.avatarUrl,
      isOnline: isOnline,
      onTap: () async {
        final navNotifier = Provider.of<NavBarVisibilityNotifier>(
          context,
          listen: false,
        );
        navNotifier.hideNavBar();
        // Navigate to UnifiedProfilePage for the member
        context.go('${AppRoutes.userProfile}/${member.id.value}');
        // No need for extra here as UnifiedProfilePage fetches its own data via BLoC
        // If UnifiedProfilePage still expects member data directly for some reason,
        // this would need to be adjusted or the page refactored.
        // However, standard practice is for the page to load its own data.

        // The original code awaited the push, which might be relevant if
        // navNotifier.showNavBar() should only run *after* returning.
        // For go_router, if you need to await navigation (e.g., for a result),
        // you might need a different pattern or to handle it within the target page's lifecycle.
        // For now, assuming direct navigation and showing navbar immediately if mounted.
        if (context.mounted) {
          // This check might be redundant if not awaiting
          navNotifier.showNavBar();
        }
      },
      trailing: _buildNotificationsCountBadge(unreadCount), // Use unreadCount
      usageContext: TileCardContext.Bubbler,
      nameColor: nameColor,
    );
  }

  // Helper to build the trailing widget for unread message count
  Widget _buildNotificationsCountBadge(int unreadCount) {
    // Use the reusable badge widget
    return NotificationsCountBadge(count: unreadCount);
  }

  Widget _buildProposeNewMemberButton(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final tileHeight = screenHeight / 12;
    // Define icon container and icon sizes consistent with _buildMoreBubblesButton
    final iconContainerSize = tileHeight * 0.7;
    final imageSize = iconContainerSize * 0.8; // Icon size inside the container

    return InkWell(
      onTap: () {
        _navBarNotifier.showNavBar();
                        context.go(AppRoutes.contacts, extra: {'showInvitePopup': true});
      },
      borderRadius: BorderRadius.circular(25),
      child: Container(
        height: tileHeight, // Match FriendsTile height
        decoration: BoxDecoration(
          color: const Color(0xFF1A2B4D), // Keep original button color
          borderRadius: BorderRadius.circular(25),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
        ), // Match FriendsTile horizontal padding
        child: Row(
          children: [
            // Replaced icon part to match _buildMoreBubblesButton
            Container(
              width: iconContainerSize,
              height: iconContainerSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: const Color(0x1AFFFFFF), // Colors.white.withValues(alpha: 0.1)
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 2,
                ),
              ),
              child: Center(
                child: Image.asset(
                  'assets/images/3d/200px/normal/plus-front.png', // Use the same image
                  width: imageSize,
                  height: imageSize,
                ),
              ),
            ),
            const SizedBox(width: 16), // Match FriendsTile spacing
            const Expanded(
              // Use Expanded to allow text to fill space if needed
              child: Text(
                'Propose a new bubbler',
                style: TextStyle(
                  fontSize: 18, // Kept original text style as per analysis
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMoreBubblesButton(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final tileHeight = screenHeight / 12;
    // Define icon container and icon sizes based on ProfileOptionTile styling
    final iconContainerSize = tileHeight * 0.7;
    final imageSize = iconContainerSize * 0.8; // Icon size inside the container

    return InkWell(
      onTap: () {
        _navigateToSubPage(const AnnouncementBannerPage());
      },
      borderRadius: BorderRadius.circular(25),
      child: Container(
        height: tileHeight, // Match FriendsTile height
        decoration: BoxDecoration(
          color: const Color(0xFF1A2B4D), // Original button color
          borderRadius: BorderRadius.circular(25),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
        ), // Match FriendsTile horizontal padding
        child: Row(
          children: [
            // Apply styling from ProfileOptionTile's icon background
            Container(
              width: iconContainerSize,
              height: iconContainerSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: const Color(0x1AFFFFFF), // Colors.white.withValues(alpha: 0.1)
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 2,
                ),
              ),
              child: Center(
                child: Image.asset(
                  'assets/images/3d/200px/normal/chat-bubble.png', // New icon
                  width: imageSize,
                  height: imageSize,
                  // Optional: Add a color filter if the png needs to be white
                  // color: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 16), // Match FriendsTile spacing
            const Expanded(
              child: Text(
                'Join more bubbles',
                style: TextStyle(
                  color: Colors.white,
                  fontSize:
                      18, // Updated font size to match 'Propose a new bubbler'
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _navBarNotifier.showNavBar();
    super.dispose();
  }

  void _showBubblerProfile(String bubblerId) {
    final navNotifier = Provider.of<NavBarVisibilityNotifier>(context, listen: false);
    navNotifier.hideNavBar();
    showDialog(
      context: context,
      builder: (context) => UnifiedProfilePage(userId: bubblerId),
    ).then((_) {
      // navNotifier.showNavBar() should only run *after* returning.
      navNotifier.showNavBar();
    });
  }
}
