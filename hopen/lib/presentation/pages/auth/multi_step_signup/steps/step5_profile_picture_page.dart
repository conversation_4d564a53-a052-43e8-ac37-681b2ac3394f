import 'dart:io'; // For File
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:image_picker/image_picker.dart';

import 'package:flutter/material.dart';

import '../../../../../provider/services/image_processing_service.dart';
import '../../../../widgets/custom_toast.dart';
import '../../../../widgets/profile_picture_widget.dart';
import '../signup_step_base.dart';

class Step5ProfilePicturePage extends SignupStepBase {

  Step5ProfilePicturePage({
    required super.currentStep,
    required super.totalSteps,
    required this.onNextCustom,
    required VoidCallback super.onBack,
    this.initialImageUrl,
    super.key,
  }) : super(
         title: 'Choose your profile picture',
         onNext: () {},
       );
  final void Function(String?) onNextCustom;
  final String? initialImageUrl;

  @override
  State<Step5ProfilePicturePage> createState() =>
      _Step5ProfilePicturePageState();
}

class _Step5ProfilePicturePageState
    extends SignupStepBaseState<Step5ProfilePicturePage> {
  File? _selectedImageFile;
  String? _processedImagePath;
  bool _isProcessingImage = false;

  @override
  void initState() {
    super.initState();

    // Restore previously selected image (if provided)
    _processedImagePath = widget.initialImageUrl;
  }

  @override
  bool isFormValid() => true;

  // This handleNext is called by the button in SignupStepBase
  @override
  void handleNext() {
    if (isFormValid()) {
      // Pass the processed image path (local file path for signup)
      widget.onNextCustom(
        _processedImagePath,
      );
    } else {
      // Show custom toast when validation fails
      CustomToast.showError(
        context,
        'Please enter all your infos to continue',
      );
    }
  }

  @override
  List<Widget> buildStepContent(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final spacingHeight = screenHeight / 48;
    final fieldHeight = screenHeight / 16;
    final avatarSize = screenHeight * 0.15;

    return [
      // Image requirements text – identical style and spacing to Step 4 notice
      Text(
        'It should be at least 640x640 pixels, in JPEG, PNG, WebP, HEIF or HEIC format.',
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.7),
          fontSize: 16,
          height: 1.5,
        ),
        textAlign: TextAlign.center,
      ),
      SizedBox(height: spacingHeight * 2),
      Center(
        child: GestureDetector(
          onTap: _selectImageOptions,
          child: _processedImagePath != null || _selectedImageFile != null
              ? ProfilePictureWidget(
                  imageUrl: _processedImagePath ?? _selectedImageFile?.path,
                  firstName: 'User',
                  lastName: 'Avatar',
                  radius: avatarSize / 2,
                )
              : Container(
                  width: avatarSize,
                  height: avatarSize,
                  decoration: ShapeDecoration(
                    shape: RoundedSuperellipseBorder(
                      borderRadius: BorderRadius.circular(avatarSize * 0.4),
                      side: BorderSide(color: Colors.blue, width: 2),
                    ),
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                  child: Icon(
                    Icons.add_a_photo,
                    color: Colors.white,
                    size: avatarSize * 0.4,
                  ),
                ),
        ),
      ),
      SizedBox(height: spacingHeight),
      SizedBox(
        height: fieldHeight,
        child: Row(
          children: [
            Expanded(
              child: _buildButton(
                label: 'Gallery',
                icon: Icons.photo_library,
                onPressed: () => _pickImage('gallery'),
              ),
            ),
            SizedBox(width: spacingHeight),
            Expanded(
              child: _buildButton(
                label: 'Camera',
                icon: Icons.camera_alt,
                onPressed: () => _pickImage('camera'),
              ),
            ),
          ],
        ),
      ),
      SizedBox(height: spacingHeight),
      // Skip for now text - matching Step 4 "Resend verification code" styling exactly
      TextButton(
        onPressed: () => widget.onNextCustom(null), // Call custom onNext for skip
        style: TextButton.styleFrom(
          padding: EdgeInsets.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          alignment: Alignment.center,
        ),
        child: Text(
          'Skip for now',
          style: TextStyle(color: Colors.white.withValues(alpha: 0.7), fontSize: 16),
        ),
      ),
    ];
  }

  Widget _buildButton({
    required String label,
    required IconData icon,
    required VoidCallback? onPressed,
  }) {
    final screenHeight = MediaQuery.of(context).size.height;
    final fieldHeight = screenHeight / 16;

    return ElevatedButton.icon(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withValues(alpha: 0.1),
        foregroundColor: Colors.white,
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(18),
          side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
        padding: EdgeInsets.zero,
        minimumSize: Size.zero,
        maximumSize: const Size(double.infinity, double.infinity),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        fixedSize: Size.fromHeight(fieldHeight),
      ),
      icon: Icon(icon),
      label: Text(label),
    );
  }

  void _selectImageOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: const Color(0xFF1E1E1E),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 20),
              const Text(
                'Select Profile Picture',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Colors.white),
                title: const Text('Camera', style: TextStyle(color: Colors.white)),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage('camera');
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library, color: Colors.white),
                title: const Text('Gallery', style: TextStyle(color: Colors.white)),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage('gallery');
                },
              ),
              if (_processedImagePath != null)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text('Remove Picture', style: TextStyle(color: Colors.red)),
                  onTap: () {
                    Navigator.pop(context);
                    _removeImage();
                  },
                ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _pickImage(String source) async {
    try {
      setState(() {
        _isProcessingImage = true;
      });

      print('🚀 Starting client-side image processing for signup...');

      // For signup flow, process locally only - no upload attempts
      XFile? imageFile;
      if (source == 'camera') {
        imageFile = await ImageProcessingService.takePhoto();
      } else {
        imageFile = await ImageProcessingService.pickFromGallery();
      }

      if (imageFile != null) {
        await _processImageLocally(imageFile);
      }
    } catch (e) {
      print('❌ Failed to process image during signup: $e');
      CustomToast.showError(
        context,
        'Failed to process image: ${e.toString()}',
      );
    } finally {
      setState(() {
        _isProcessingImage = false;
      });
    }
  }

  Future<void> _processImageLocally(XFile imageFile) async {
    try {
      print('🔄 Starting client-side image processing...');
      final originalSize = await imageFile.length();
      print('📁 Original image size: ${(originalSize / 1024 / 1024).toStringAsFixed(2)}MB');

      // Process image completely on client side: resize to 1440x1440 and convert to WebP
      print('✨ Processing strategy: Client-side resize to 1440x1440 and WebP conversion');
      final processedBytes = await ImageProcessingService.processImageFromFile(imageFile);

      print('📊 Client-side processing: ${(originalSize / 1024 / 1024).toStringAsFixed(2)}MB → ${(processedBytes.length / 1024 / 1024).toStringAsFixed(2)}MB');
      print('📐 Final dimensions: 1440x1440 pixels');
      print('🎯 Format: WebP (ready for backend storage)');

      // Store locally for signup flow - will be uploaded after account creation
      final tempDir = await Directory.systemTemp.createTemp('hopen_signup_');
      final fileName = '${const Uuid().v4()}.webp';
      final localPath = path.join(tempDir.path, fileName);
      final localFile = File(localPath);
      await localFile.writeAsBytes(processedBytes);

      print('💾 Stored locally for signup: $localPath');

      setState(() {
        _selectedImageFile = localFile;
        _processedImagePath = localPath; // Use local path during signup
      });

      CustomToast.showSuccess(context, 'Profile picture ready for signup!');
      print('✅ Client-side processing completed successfully');
    } catch (e) {
      print('❌ Failed to process image locally: $e');
      throw Exception('Failed to process image locally: $e');
    }
  }

  void _removeImage() {
    setState(() {
      _processedImagePath = null;
      _selectedImageFile = null;
    });

    CustomToast.show(context, 'Profile picture removed');
  }


}
