import 'package:equatable/equatable.dart';

class BubbleRequestModel extends Equatable {

  const BubbleRequestModel({
    required this.id,
    required this.bubbleId,
    required this.requesterId,
    required this.targetId,
    required this.type,
    required this.status,
    required this.createdAt,
    this.message,
  });

  final String id;
  final String bubbleId;
  final String requesterId;
  final String targetId;
  final String type;
  final String status;
  final String? message;
  final DateTime createdAt;

  factory BubbleRequestModel.fromJson(Map<String, dynamic> json) {
    return BubbleRequestModel(
      id: json['id'] as String,
      bubbleId: json['bubble_id'] as String? ?? '', // Handle null for start requests
      requesterId: json['requester_id'] as String,
      targetId: json['target_id'] as String? ?? json['recipient_id'] as String, // Handle both field names
      type: json['type'] as String? ?? json['request_type'] as String, // Handle both field names
      status: json['status'] as String,
      message: json['message'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() => {
      'id': id,
      'bubble_id': bubbleId,
      'requester_id': requesterId,
      'target_id': targetId,
      'type': type,
      'status': status,
      'message': message,
      'created_at': createdAt.toIso8601String(),
    };

  @override
  List<Object?> get props => [id, bubbleId, requesterId, targetId, type, status, message, createdAt];
}