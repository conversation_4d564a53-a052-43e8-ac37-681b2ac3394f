import 'package:equatable/equatable.dart';

/// Enum for notification categories based on technical documentation
enum NotificationCategory {
  // Contact/Friend Requests
  contactRequestReceived,
  contactRequestAccepted,
  contactRequestDeclined,
  friendshipEstablished,

  // Bubble Invitations & Join Requests
  bubbleInvitationReceived,
  bubbleJoinRequestReceived,
  bubbleJoinRequestAccepted,
  bubbleJoinRequestRejected,
  bubbleMemberJoined,

  // Bubble Management & Interaction
  bubbleVotekickInitiated,
  bubbleVotekickPassed,

  // Bubble Messages & Calls
  bubbleChatMessageReceived,
  bubbleVoiceMessageReceived,
  bubbleVideoMessageReceived,
  bubbleAudioCallIncoming,
  bubbleVideoCallIncoming,
  bubbleScreenShareIncoming,
  bubbleCallInProgress,
  bubbleCallEnded,
  bubbleMissedCall,

  // Bubble Lifecycle
  bubblePopReminder60Days,
  bubblePopReminder30Days,
  bubblePopReminder20Days,
  bubblePopReminder10Days,
  bubblePopReminder7Days,
  bubblePopReminder3Days,
  bubblePopReminder24Hours,

  // Direct Friend Interactions
  friendChatMessageReceived,
  friendVoiceMessageReceived,
  friendVideoMessageReceived,
  friendAudioCallIncoming,
  friendVideoCallIncoming,
  friendScreenShareIncoming,
  friendMissedCall,
  friendCallInProgress,

  // User Activity & Engagement
  inactiveNoBubble1Day,
  inactiveNoBubble2Days,
  inactiveNoBubble3Days,
  inactiveNoBubble7Days,

  // General Categories
  statusUpdates,
  securityAlerts,
  appUpdates,

  // Legacy categories for backward compatibility
  @Deprecated('Use specific notification types instead')
  message,
  @Deprecated('Use bubbleInvitationReceived instead')
  bubbleInvite,
  @Deprecated('Use contactRequestReceived instead')
  contactRequest,
  @Deprecated('Use statusUpdates instead')
  system,
  @Deprecated('Use specific call types instead')
  call,
  @Deprecated('Use specific reminder types instead')
  reminder,
}

/// Enum for notification types (legacy support)
enum NotificationType {
  message,
  bubbleInvite,
  contactRequest,
  system,
  call,
  reminder,
}

/// Enum for notification priority
enum NotificationPriority { low, normal, high, urgent }

/// Core notification model
class Notification extends Equatable {
  const Notification({
    required this.id,
    required this.message,
    required this.category,
    required this.isRead,
    required this.createdAt,
    this.description,
    this.title,
    this.priority = NotificationPriority.normal,
    this.readAt,
    this.payload,
    this.imageUrl,
    this.actionUrl,
  });

  factory Notification.fromJson(Map<String, dynamic> json) => Notification(
    id: json['id'] as String,
    message: json['message'] as String,
    description: json['description'] as String?,
    title: json['title'] as String?,
    category: NotificationCategory.values.firstWhere(
      (e) => e.name == json['category'],
      orElse: () => NotificationCategory.system,
    ),
    priority: NotificationPriority.values.firstWhere(
      (e) => e.name == json['priority'],
      orElse: () => NotificationPriority.normal,
    ),
    isRead: json['isRead'] as bool,
    createdAt: DateTime.parse(json['createdAt'] as String),
    readAt:
        json['readAt'] != null
            ? DateTime.parse(json['readAt'] as String)
            : null,
    payload: json['payload'] as Map<String, dynamic>?,
    imageUrl: json['imageUrl'] as String?,
    actionUrl: json['actionUrl'] as String?,
  );
  final String id;
  final String message;
  final String? description;
  final String? title; // Added title field
  final NotificationCategory category;
  final NotificationPriority priority;
  final bool isRead;
  final DateTime createdAt;
  final DateTime? readAt;
  final Map<String, dynamic>? payload;
  final String? imageUrl;
  final String? actionUrl;

  @override
  List<Object?> get props => [
    id,
    message,
    description,
    title,
    category,
    priority,
    isRead,
    createdAt,
    readAt,
    payload,
    imageUrl,
    actionUrl,
  ];

  Notification copyWith({
    String? id,
    String? message,
    String? description,
    String? title,
    NotificationCategory? category,
    NotificationPriority? priority,
    bool? isRead,
    DateTime? createdAt,
    DateTime? readAt,
    Map<String, dynamic>? payload,
    String? imageUrl,
    String? actionUrl,
    bool clearReadAt = false,
  }) => Notification(
    id: id ?? this.id,
    message: message ?? this.message,
    description: description ?? this.description,
    title: title ?? this.title,
    category: category ?? this.category,
    priority: priority ?? this.priority,
    isRead: isRead ?? this.isRead,
    createdAt: createdAt ?? this.createdAt,
    readAt: clearReadAt ? null : (readAt ?? this.readAt),
    payload: payload ?? this.payload,
    imageUrl: imageUrl ?? this.imageUrl,
    actionUrl: actionUrl ?? this.actionUrl,
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'message': message,
    'description': description,
    'title': title,
    'category': category.name,
    'priority': priority.name,
    'isRead': isRead,
    'createdAt': createdAt.toIso8601String(),
    'readAt': readAt?.toIso8601String(),
    'payload': payload,
    'imageUrl': imageUrl,
    'actionUrl': actionUrl,
  };
}
