import 'package:equatable/equatable.dart';
import '../../core/models/notification_model.dart';

/// Base class for all notification events
abstract class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object> get props => [];
}

/// Event to fetch all notifications
class FetchNotifications extends NotificationEvent {
  const FetchNotifications();
}

/// Event to fetch notification count
class FetchUnreadCount extends NotificationEvent {
  const FetchUnreadCount();
}

/// Event when a new notification is received
class NotificationReceived extends NotificationEvent {
  const NotificationReceived(this.notification);
  final Notification notification;

  @override
  List<Object> get props => [notification];
}

/// Event to mark a notification as read
class MarkNotificationAsRead extends NotificationEvent {
  const MarkNotificationAsRead(this.notificationId);
  final String notificationId;

  @override
  List<Object> get props => [notificationId];
}

/// Event to mark a notification as unread
class MarkNotificationAsUnread extends NotificationEvent {
  const MarkNotificationAsUnread(this.notificationId);
  final String notificationId;

  @override
  List<Object> get props => [notificationId];
}

/// Event to mark all notifications as read
class MarkAllNotificationsAsRead extends NotificationEvent {
  const MarkAllNotificationsAsRead();
}

/// Event to delete a notification
class DeleteNotification extends NotificationEvent {
  const DeleteNotification(this.notificationId);
  final String notificationId;

  @override
  List<Object> get props => [notificationId];
}

/// Event to delete all notifications
class DeleteAllNotifications extends NotificationEvent {
  const DeleteAllNotifications();
}

/// Event to add a new notification
class AddNotificationEvent extends NotificationEvent {
  const AddNotificationEvent({required this.notification});
  final Notification notification;

  @override
  List<Object> get props => [notification];
}

/// Legacy alias for AddNotificationEvent
class AddNotification extends AddNotificationEvent {
  const AddNotification({required super.notification});
}

/// Event when a contact request notification is received
class ContactRequestNotificationReceivedEvent extends NotificationEvent {
  const ContactRequestNotificationReceivedEvent({
    required this.requestId,
    required this.requesterId,
    required this.requesterName,
    required this.requesterUsername,
    required this.requesterProfilePicUrl,
    required this.requestTimestamp,
  });

  final String requestId;
  final String requesterId;
  final String requesterName;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;
  final DateTime requestTimestamp;

  @override
  List<Object> get props => [
    requestId,
    requesterId,
    requesterName,
    requesterUsername ?? '',
    requesterProfilePicUrl ?? '',
    requestTimestamp,
  ];
}
