import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../repositories/notification/notification_repository.dart';
import '../../core/models/notification_model.dart';
import 'notification_event.dart';
import 'notification_state.dart';

/// Bloc for managing notification state
class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {

  NotificationBloc({required NotificationRepository notificationRepository})
    : _notificationRepository = notificationRepository,
      super(const NotificationInitial()) {
    on<FetchNotifications>(_onFetchNotifications);
    on<FetchUnreadCount>(_onFetchUnreadCount);
    on<NotificationReceived>(_onNotificationReceived);
    on<MarkNotificationAsRead>(_onMarkNotificationAsRead);
    on<MarkNotificationAsUnread>(_onMarkNotificationAsUnread);
    on<MarkAllNotificationsAsRead>(_onMarkAllNotificationsAsRead);
    on<DeleteNotification>(_onDeleteNotification);
    on<DeleteAllNotifications>(_onDeleteAllNotifications);
    on<AddNotificationEvent>(_onAddNotification);
    on<AddNotification>(_onAddNotification);
    on<ContactRequestNotificationReceivedEvent>(_onContactRequestNotificationReceived);



    // Listen to repository streams
    _notificationsSubscription = _notificationRepository
        .notificationsStream()
        .listen((notifications) {
          add(const FetchNotifications());
        });

    _unreadCountSubscription = _notificationRepository
        .unreadCountStream()
        .listen((count) {
          add(FetchUnreadCount());
        });

    // Listen to notification stream
    _notificationSubscription = _notificationRepository.notificationsStream().listen(
      (notifications) {
        if (notifications.isNotEmpty) {
          add(NotificationReceived(notifications.first));
        }
      },
    );

    // Setup Firebase messaging handlers
    _setupFirebaseMessaging();
  }
  final NotificationRepository _notificationRepository;
  StreamSubscription<List<Notification>>? _notificationsSubscription;
  StreamSubscription<int>? _unreadCountSubscription;
  StreamSubscription<List<Notification>>? _notificationSubscription;

  void _setupFirebaseMessaging() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle background messages when app is opened
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);
  }

  void _handleForegroundMessage(RemoteMessage message) {
    print('Handling foreground message: ${message.messageId}');
    
    // Process the message and add to state
    final notification = _processRemoteMessage(message);
    if (notification != null) {
      add(AddNotificationEvent(notification: notification));
    }
  }

  void _handleBackgroundMessage(RemoteMessage message) {
    print('Handling background message: ${message.messageId}');
    
    // Process the message and add to state
    final notification = _processRemoteMessage(message);
    if (notification != null) {
      add(AddNotificationEvent(notification: notification));
    }
  }

  Notification? _processRemoteMessage(RemoteMessage message) {
    try {
      return Notification(
        id: message.messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
        message: message.notification?.title ?? 'New notification',
        description: message.notification?.body ?? '',
        category: NotificationCategory.system,
        isRead: false,
        createdAt: DateTime.now(),
        payload: message.data,
      );
    } catch (e) {
      print('Error processing remote message: $e');
      return null;
    }
  }

  Future<void> _onFetchNotifications(
    FetchNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(const NotificationLoading());
      final notifications = await _notificationRepository.getNotifications();
      final unreadCount = await _notificationRepository.getUnreadCount();
      emit(
        NotificationsLoaded(
          notifications: notifications,
          unreadCount: unreadCount,
        ),
      );
    } catch (e) {
      print('🔔 NotificationBloc: Error fetching notifications: $e');
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onFetchUnreadCount(
    FetchUnreadCount event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final unreadCount = await _notificationRepository.getUnreadCount();
      if (state is NotificationsLoaded) {
        final currentState = state as NotificationsLoaded;
        emit(currentState.copyWith(unreadCount: unreadCount));
      }
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  void _onNotificationReceived(
    NotificationReceived event,
    Emitter<NotificationState> emit,
  ) {
    // Handle specific notification types
    final notification = event.notification;

    // Check if this is a bubble expiry notification
    if (notification.payload != null &&
        notification.payload!['type'] == 'bubble_expiry') {
      // Extract bubble information from notification payload
      final payload = notification.payload!;
      if (payload['bubble_id'] != null && payload['days_left'] != null) {
        final bubbleId = payload['bubble_id'] as String;
        final daysLeft = payload['days_left'] as int;

        // Emit a special state to trigger UI dialog
        emit(BubbleExpiryNotificationReceived(
          notification: notification,
          bubbleId: bubbleId,
          daysLeft: daysLeft,
        ));
      }
    }

    // No other direct action needed as the repository stream will trigger a state update
  }

  Future<void> _onMarkNotificationAsRead(
    MarkNotificationAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    print(
      '[NotificationBloc] Received MarkNotificationAsRead for ID: ${event.notificationId}',
    ); // Log event
    if (state is NotificationsLoaded) {
      final currentState = state as NotificationsLoaded;
      final currentNotifications = List<Notification>.from(
        currentState.notifications,
      );
      final index = currentNotifications.indexWhere(
        (n) => n.id == event.notificationId,
      );

      print(
        '[NotificationBloc] Found index: $index for ID: ${event.notificationId}',
      ); // Log index

      if (index != -1) {
        final originalNotification = currentNotifications[index];
        print(
          '[NotificationBloc] Notification at index $index BEFORE update: ID=${originalNotification.id}, isRead=${originalNotification.isRead}',
        ); // Log before state

        if (!originalNotification.isRead) {
          final updatedNotification = originalNotification.copyWith(
            isRead: true,
          );
          print(
            '[NotificationBloc] Notification at index $index AFTER copyWith: ID=${updatedNotification.id}, isRead=${updatedNotification.isRead}',
          ); // Log after copyWith

          currentNotifications[index] = updatedNotification;

          final newUnreadCount =
              (currentState.unreadCount > 0) ? currentState.unreadCount - 1 : 0;
          print(
            '[NotificationBloc] Emitting new state with ${currentNotifications.length} notifications and unread count $newUnreadCount',
          ); // Log emitted state info
          // You could even print the entire list here for detailed debugging if needed:
          // print('[NotificationBloc] Emitting list: ${currentNotifications.map((n)=>'id:${n.id}, read:${n.isRead}').toList()}');

          emit(
            currentState.copyWith(
              notifications: currentNotifications,
              unreadCount: newUnreadCount,
            ),
          );
        } else {
          print(
            '[NotificationBloc] Notification ${event.notificationId} already marked read in UI state.',
          );
        }

        try {
          await _notificationRepository.markAsRead(event.notificationId);
        } catch (e) {
          print(
            '[NotificationBloc] Error marking notification ${event.notificationId} as read in repository: $e',
          );
        }
      } else {
        print(
          '[NotificationBloc] Notification ${event.notificationId} not found in current state for marking as read.',
        );
      }
    }
  }

  Future<void> _onMarkNotificationAsUnread(
    MarkNotificationAsUnread event,
    Emitter<NotificationState> emit,
  ) async {
    // Only proceed if the current state is NotificationsLoaded
    if (state is NotificationsLoaded) {
      final currentState = state as NotificationsLoaded;
      // Create mutable copy of the list
      final currentNotifications = List<Notification>.from(
        currentState.notifications,
      );

      // Find the index of the notification to update
      final index = currentNotifications.indexWhere(
        (n) => n.id == event.notificationId,
      );

      if (index != -1) {
        // Ensure the notification is actually marked as read in the current state
        if (currentNotifications[index].isRead) {
          // Update the specific notification in the copied list using copyWith
          currentNotifications[index] = currentNotifications[index].copyWith(
            isRead: false,
          );

          // Emit the updated state immediately for responsiveness
          // Increment unreadCount optimistically
          final newUnreadCount = currentState.unreadCount + 1;
          emit(
            currentState.copyWith(
              notifications: currentNotifications,
              unreadCount: newUnreadCount, // Optimistically update count
            ),
          );
        } else {
          // Already unread in current state
          print(
            'Notification ${event.notificationId} already marked unread in UI state.',
          );
        }

        try {
          // Call the repository to persist the change
          await _notificationRepository.markAsUnread(event.notificationId);
          // Stream will handle source-of-truth update
        } catch (e) {
          print(
            'Error marking notification ${event.notificationId} as unread in repository: $e',
          );
          // Optionally revert optimistic update
          // emit(currentState);
        }
      } else {
        print(
          'Notification ${event.notificationId} not found in current state for marking as unread.',
        );
      }
    }
  }

  Future<void> _onMarkAllNotificationsAsRead(
    MarkAllNotificationsAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    // Optimistic update for MarkAllAsRead
    if (state is NotificationsLoaded) {
      final currentState = state as NotificationsLoaded;
      if (currentState.unreadCount > 0) {
        // Only update if there are unread ones
        final currentNotifications = List<Notification>.from(
          currentState.notifications,
        );
        // Mark all as read in the copied list
        for (var i = 0; i < currentNotifications.length; i++) {
          if (!currentNotifications[i].isRead) {
            currentNotifications[i] = currentNotifications[i].copyWith(
              isRead: true,
            );
          }
        }
        // Emit the updated state immediately
        emit(
          currentState.copyWith(
            notifications: currentNotifications,
            unreadCount: 0,
          ),
        );
      }
      try {
        await _notificationRepository.markAllAsRead();
        // Stream will handle source-of-truth update
      } catch (e) {
        emit(NotificationError(e.toString()));
        // Consider reverting optimistic update if repo fails
        // emit(currentState);
      }
    } else {
      // If not loaded, just call repo (or handle error)
      try {
        await _notificationRepository.markAllAsRead();
      } catch (e) {
        emit(NotificationError(e.toString()));
      }
    }
  }

  Future<void> _onDeleteNotification(
    DeleteNotification event,
    Emitter<NotificationState> emit,
  ) async {
    // Optimistic update for DeleteNotification
    if (state is NotificationsLoaded) {
      final currentState = state as NotificationsLoaded;
      final currentNotifications = List<Notification>.from(
        currentState.notifications,
      );
      var newUnreadCount = currentState.unreadCount;
      var wasUnread = false;

      // Find the index and check if it was unread before removing
      final index = currentNotifications.indexWhere(
        (n) => n.id == event.notificationId,
      );
      if (index != -1) {
        wasUnread = !currentNotifications[index].isRead;
        currentNotifications.removeAt(index); // Remove from the copied list

        // Decrement unread count only if the removed item was unread
        if (wasUnread && newUnreadCount > 0) {
          newUnreadCount--;
        }

        // Emit the updated state immediately
        emit(
          currentState.copyWith(
            notifications: currentNotifications,
            unreadCount: newUnreadCount,
          ),
        );
      } else {
        print(
          'Notification ${event.notificationId} not found in current state for deletion.',
        );
        // Optionally still attempt repository deletion if ID exists but not in state
      }

      try {
        // Call the repository to persist the change
        await _notificationRepository.deleteNotification(event.notificationId);
        // Stream will handle source-of-truth update if needed (e.g., confirms deletion)
      } catch (e) {
        print(
          'Error deleting notification ${event.notificationId} in repository: $e',
        );
        // Optionally revert optimistic update or show specific error
        // emit(currentState);
      }
    } else {
      // If not loaded, just call repo (or handle error)
      try {
        await _notificationRepository.deleteNotification(event.notificationId);
      } catch (e) {
        emit(NotificationError(e.toString()));
      }
    }
  }

  Future<void> _onDeleteAllNotifications(DeleteAllNotifications event, Emitter<NotificationState> emit) async {
    try {
      emit(const NotificationLoading());
      await _notificationRepository.deleteAllNotifications();
      emit(const NotificationsLoaded(notifications: [], unreadCount: 0));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onAddNotification(AddNotificationEvent event, Emitter<NotificationState> emit) async {
    try {
      // For now, just emit the current state since addNotification doesn't exist
      // The repository will emit updated notifications via stream
      // await _notificationRepository.addNotification(event.notification);
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  void _onContactRequestNotificationReceived(
    ContactRequestNotificationReceivedEvent event,
    Emitter<NotificationState> emit,
  ) {
    print('🔔 NotificationBloc: Contact request notification received for ${event.requesterName}');

    // Emit the contact request notification state
    // This will trigger BlocListener in the UI to show the dialog
    emit(ContactRequestNotificationReceived(
      requestId: event.requestId,
      requesterId: event.requesterId,
      requesterName: event.requesterName,
      requesterUsername: event.requesterUsername,
      requesterProfilePicUrl: event.requesterProfilePicUrl,
      requestTimestamp: event.requestTimestamp,
    ));
  }



  @override
  Future<void> close() {
    _notificationsSubscription?.cancel();
    _unreadCountSubscription?.cancel();
    _notificationSubscription?.cancel();
    return super.close();
  }
}
