import 'package:equatable/equatable.dart';

import '../../core/models/profile_picture_result.dart';

/// Base class for all profile picture states
abstract class ProfilePictureState extends Equatable {
  const ProfilePictureState();

  @override
  List<Object?> get props => [];
}

/// Initial state when no operation has been performed
class ProfilePictureInitial extends ProfilePictureState {
  const ProfilePictureInitial();
}

/// State when a profile picture operation is in progress
class ProfilePictureLoading extends ProfilePictureState {
  const ProfilePictureLoading({
    this.operation,
    this.progress,
  });

  final String? operation; // e.g., "Uploading...", "Processing...", "Validating..."
  final double? progress; // Upload progress 0.0 to 1.0

  @override
  List<Object?> get props => [operation, progress];
}

/// State when profile picture operation completed successfully
class ProfilePictureSuccess extends ProfilePictureState {
  const ProfilePictureSuccess({
    required this.result,
    this.message,
  });

  final ProfilePictureResult result;
  final String? message;

  @override
  List<Object?> get props => [result, message];
}

/// State when profile picture operation failed
class ProfilePictureError extends ProfilePictureState {
  const ProfilePictureError({
    required this.message,
    this.technicalDetails,
    this.isRetryable = false,
    this.retryAfter,
    this.errorCode,
    this.errorType = ProfilePictureErrorType.unknown,
    this.retryCount = 0,
  });

  final String message;
  final String? technicalDetails;
  final bool isRetryable;
  final Duration? retryAfter;
  final String? errorCode;
  final ProfilePictureErrorType errorType;
  final int retryCount;

  @override
  List<Object?> get props => [message, technicalDetails, isRetryable, retryAfter, errorCode, errorType, retryCount];
}

/// Types of profile picture errors for specific handling
enum ProfilePictureErrorType {
  network,        // Network connectivity issues
  storage,        // Storage/upload failures
  validation,     // Image validation failures
  authentication, // Auth-related failures
  fileSize,       // File too large
  fileFormat,     // Unsupported format
  processingFailed, // Image processing failures
  unknown,        // Unknown/generic errors
}

/// State when user cancelled the operation
class ProfilePictureCancelled extends ProfilePictureState {
  const ProfilePictureCancelled();
}

/// State when image validation completed
class ProfilePictureValidated extends ProfilePictureState {
  const ProfilePictureValidated({
    required this.isValid,
    this.error,
  });

  final bool isValid;
  final String? error;

  @override
  List<Object?> get props => [isValid, error];
}

/// State when profile picture was removed successfully
class ProfilePictureRemoved extends ProfilePictureState {
  const ProfilePictureRemoved();
}

/// State when image has been processed locally (for signup flow)
class ProfilePictureProcessedLocally extends ProfilePictureState {
  const ProfilePictureProcessedLocally({
    required this.localPath,
    this.message,
  });

  final String localPath;
  final String? message;

  @override
  List<Object?> get props => [localPath, message];
}
