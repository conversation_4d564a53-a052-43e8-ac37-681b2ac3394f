import 'package:equatable/equatable.dart';

/// Base class for all profile picture events
abstract class ProfilePictureEvent extends Equatable {
  const ProfilePictureEvent();

  @override
  List<Object?> get props => [];
}

/// Event to pick an image from gallery
class PickFromGalleryEvent extends ProfilePictureEvent {
  const PickFromGalleryEvent();
}

/// Event to take a photo with camera
class TakePhotoEvent extends ProfilePictureEvent {
  const TakePhotoEvent();
}

/// Event to remove/delete current profile picture
class RemoveProfilePictureEvent extends ProfilePictureEvent {
  const RemoveProfilePictureEvent({required this.imageUrl});

  final String imageUrl;

  @override
  List<Object?> get props => [imageUrl];
}

/// Event to validate an image before upload
class ValidateImageEvent extends ProfilePictureEvent {
  const ValidateImageEvent({required this.imagePath});

  final String imagePath;

  @override
  List<Object?> get props => [imagePath];
}

/// Event to reset the profile picture state
class ResetProfilePictureEvent extends ProfilePictureEvent {
  const ResetProfilePictureEvent();
}

/// Event to retry a failed upload operation
class RetryUploadEvent extends ProfilePictureEvent {
  const RetryUploadEvent({
    required this.lastOperation,
    this.imageUrl,
  });

  final ProfilePictureOperation lastOperation;
  final String? imageUrl; // Required for remove operation retry

  @override
  List<Object?> get props => [lastOperation, imageUrl];
}

/// Event to process image locally without upload (for signup flow)
class ProcessImageLocallyEvent extends ProfilePictureEvent {
  const ProcessImageLocallyEvent({required this.imagePath});

  final String imagePath;

  @override
  List<Object?> get props => [imagePath];
}

/// Event to pick image from gallery for local processing only
class PickFromGalleryLocalEvent extends ProfilePictureEvent {
  const PickFromGalleryLocalEvent();
}

/// Event to take photo for local processing only
class TakePhotoLocalEvent extends ProfilePictureEvent {
  const TakePhotoLocalEvent();
}

/// Enum for profile picture operations
enum ProfilePictureOperation {
  pickFromGallery,
  takePhoto,
  removeProfilePicture,
  processLocally,
}
