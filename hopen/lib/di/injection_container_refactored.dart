import 'package:get_it/get_it.dart';

import '../config/app_config.dart';
import '../provider/datasources/active_bubble_remote_datasource.dart';
import '../provider/datasources/bubble_remote_datasource.dart';
import '../provider/datasources/contacts_remote_datasource.dart';
import '../provider/datasources/http_remote_datasource.dart';
import '../provider/local_storage/friend_selection_cache.dart';
import '../provider/repositories/active_bubble/active_bubble_repository_impl.dart'
    as active_bubble_impl;
import '../provider/repositories/analytics/analytics_repository_impl.dart';
import '../provider/repositories/bubble/bubble_repository_impl.dart'
    as bubble_impl;
import '../provider/repositories/bubble/bubble_join_request_repository_impl.dart';
import '../provider/repositories/bubble/bubble_invite_request_repository_impl.dart';
import '../provider/repositories/bubble_friend_selection/bubble_friend_selection_repository_impl.dart';
import '../provider/repositories/bubble_history/bubble_history_repository_impl.dart';
import '../provider/repositories/call/call_repository_impl.dart';

import '../provider/repositories/contact_request_repository_impl.dart';
import '../provider/repositories/contacts/contacts_repository_impl.dart';
import '../provider/repositories/friend_selection_repository_impl.dart';
import '../provider/repositories/notification/notification_repository_impl.dart';
import '../provider/repositories/user/user_repository_impl.dart';
import '../provider/repositories/webrtc/webrtc_repository_impl.dart';
import '../provider/repositories/profile_picture/profile_picture_repository_impl.dart';
import '../provider/repositories/availability/availability_repository_impl.dart';
import '../provider/repositories/activity_status/activity_status_repository_impl.dart';
import '../provider/repositories/local_storage/user_settings_repository_impl.dart';
import '../provider/repositories/report/report_repository_impl.dart';

import '../provider/services/local_storage/local_storage_service.dart';
import '../provider/services/report_service.dart';
import '../provider/services/webrtc/webrtc_service.dart';
import '../provider/services/connectivity/connectivity_service.dart';
import '../provider/services/real_time_notification_service.dart';
import '../provider/services/real_time_service_manager.dart';
import '../provider/services/notification_service_fcm.dart';
import '../statefulbusinesslogic/core/services/notification_orchestrator.dart';
import '../presentation/services/dialog_service_impl.dart';
import '../statefulbusinesslogic/core/services/connectivity_service.dart' as core;
import '../repositories/active_bubble/active_bubble_repository.dart';
import '../repositories/analytics/analytics_repository.dart';
import '../repositories/bubble/bubble_repository.dart';
import '../repositories/bubble/bubble_join_request_repository.dart';
import '../repositories/bubble/bubble_invite_request_repository.dart';
import '../repositories/bubble_friend_selection/bubble_friend_selection_repository.dart';
import '../repositories/bubble_history/bubble_history_repository.dart';
import '../repositories/call/call_repository.dart';

import '../repositories/contact/contact_request_repository.dart';
import '../repositories/contacts/contacts_repository.dart';
import '../repositories/notification/notification_repository.dart';
import '../repositories/user/user_repository.dart';
import '../repositories/webrtc/webrtc_repository.dart';
import '../repositories/profile_picture/profile_picture_repository.dart';
import '../repositories/availability/availability_repository.dart';
import '../repositories/activity_status/activity_status_repository.dart';
import '../repositories/local_storage/user_settings_repository.dart';
import '../repositories/report/report_repository.dart';
import '../statefulbusinesslogic/bloc/active_bubble/active_bubble_bloc.dart';
import '../statefulbusinesslogic/bloc/bubble/bubble_bloc.dart';
import '../statefulbusinesslogic/bloc/bubble_history/bubble_history_bloc.dart';
import '../statefulbusinesslogic/bloc/bubble_invite_request/bubble_invite_request_bloc.dart';
import '../statefulbusinesslogic/bloc/bubble_join_request/bubble_join_request_bloc.dart';
import '../statefulbusinesslogic/bloc/bubble_kickout_request/bubble_kickout_request_bloc.dart';
import '../statefulbusinesslogic/bloc/bubble_request/bubble_request_bloc.dart';
import '../statefulbusinesslogic/bloc/call/call_bloc.dart';
import '../statefulbusinesslogic/bloc/contact_request/contact_request_bloc.dart';

import '../statefulbusinesslogic/bloc/contacts/contacts_bloc.dart';
import '../statefulbusinesslogic/bloc/friend_selection/friend_selection_bloc.dart';
import '../statefulbusinesslogic/bloc/friends/friends_bloc.dart';
import '../statefulbusinesslogic/bloc/notification/notification_bloc.dart';
import '../statefulbusinesslogic/bloc/theme/theme_bloc.dart';
import '../statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart';
import '../statefulbusinesslogic/bloc/user_profile/user_profile_bloc.dart';
import '../statefulbusinesslogic/bloc/profile_picture/profile_picture_bloc.dart';
import '../statefulbusinesslogic/bloc/availability/availability_bloc.dart';
import '../statefulbusinesslogic/bloc/activity_status/activity_status_bloc.dart';
import '../statefulbusinesslogic/bloc/voice_recording/voice_recording_bloc.dart';
import '../statefulbusinesslogic/bloc/report/report_bloc.dart';
import '../statefulbusinesslogic/core/services/call_notification_service.dart';
import '../statefulbusinesslogic/core/services/bloc_notification_service.dart';
import '../statefulbusinesslogic/core/services/activity_status_service.dart';
import '../statefulbusinesslogic/core/services/logging_service.dart';
import '../statefulbusinesslogic/bloc/notification/notification_event.dart';
import '../statefulbusinesslogic/bloc/call/call_event.dart';
import '../statefulbusinesslogic/core/models/notification_model.dart';
import '../statefulbusinesslogic/core/services/call_state_service.dart';
import '../provider/services/call_state_service_impl.dart';
import 'modules/auth_module.dart';
import 'modules/chat_module.dart';
import 'modules/core_module.dart';
import 'modules/database_module.dart';
import 'modules/performance_module.dart';

import '../statefulbusinesslogic/bloc/home_navigation/home_navigation_bloc.dart';

final sl = GetIt.instance;

/// Initialize dependency injection with proper async handling and modular structure
Future<void> init({bool isTestEnvironment = false}) async {
  // Set test environment flag
  if (isTestEnvironment) {
    // Configure test-specific settings
    sl.registerSingleton<bool>(true, instanceName: 'isTestEnvironment');
  }

  // Register core modules (performance module first for startup monitoring)
  await PerformanceModule.register(sl);
  registerCoreModule(sl);
  registerDatabaseModule(sl);
  registerAuthModule(sl);
  registerChatModule(sl);

  // Wait for async dependencies before proceeding
  await sl.allReady();

  // Register remaining data sources
  _registerDataSources();

  // Register repositories
  _registerRepositories();

  // Register services
  _registerServices();

  // Register BLoCs
  _registerBlocs();
}

/// Register data sources
void _registerDataSources() {
  sl.registerLazySingleton<HttpRemoteDataSource>(
    () => HttpRemoteDataSourceImpl(
      apiService: sl(),
    ),
  );

  sl.registerLazySingleton<ContactsRemoteDataSource>(
    () => ContactsRemoteDataSourceImpl(apiService: sl()),
  );

  sl.registerLazySingleton<BubbleRemoteDataSource>(
    () => BubbleRemoteDataSourceImpl(apiService: sl()),
  );

  sl.registerLazySingleton<ActiveBubbleRemoteDataSource>(
    () => ActiveBubbleRemoteDataSourceImpl(apiService: sl()),
  );
}

/// Register repositories with proper dependency injection
void _registerRepositories() {
  // Core repositories
  sl.registerLazySingleton<UserRepository>(
    () => UserRepositoryImpl(httpDataSource: sl()),
  );

  // Local Storage Service - Register as async singleton
  sl.registerSingletonAsync<LocalStorageService>(
    () async {
      final service = LocalStorageService();
      await service.init(); // Initialize SharedPreferences
      return service;
    },
  );

  // Notification Repository - depends on LocalStorageService
  sl.registerSingletonWithDependencies<NotificationRepository>(
    () => NotificationRepositoryImpl(localStorage: sl<LocalStorageService>()),
    dependsOn: [LocalStorageService],
  );


  sl.registerLazySingleton<ContactsRepository>(
    () => ContactsRepositoryImpl(
      apiService: sl(),
      remoteDataSource: sl(),
      httpDataSource: sl(),
    ),
  );

  // Bubble repositories
  sl.registerLazySingleton<BubbleRepository>(
    () => bubble_impl.BubbleRepositoryImpl(remoteDataSource: sl()),
  );

  sl.registerLazySingleton<ActiveBubbleRepository>(
    () => active_bubble_impl.ActiveBubbleRepositoryImpl(remoteDataSource: sl()),
  );

  sl.registerLazySingleton<BubbleHistoryRepository>(
    () => BubbleHistoryRepositoryImpl(remoteDataSource: sl()),
  );

  sl.registerLazySingleton<BubbleFriendSelectionRepository>(
    () => BubbleFriendSelectionRepositoryImpl(remoteDataSource: sl()),
  );

  sl.registerLazySingleton<BubbleJoinRequestRepository>(
    () => BubbleJoinRequestRepositoryImpl(remoteDataSource: sl()),
  );

  sl.registerLazySingleton<BubbleInviteRequestRepository>(
    () => BubbleInviteRequestRepositoryImpl(remoteDataSource: sl()),
  );

  // Call repository
  sl.registerLazySingleton<CallRepositoryExtended>(
    () => CallRepositoryImpl(
      webrtcService: sl(),
      mqttService: sl(),
      userRepository: sl(),
      bubbleRepository: sl(),
      authRepository: sl(),
    ),
  );

  // Request repositories
  sl.registerLazySingleton<ContactRequestRepository>(
    () =>
        ContactRequestRepositoryImpl(apiService: sl(), remoteDataSource: sl()),
  );

  sl.registerLazySingleton<FriendSelectionRepository>(
    () => FriendSelectionRepositoryImpl(dataSource: sl()),
  );

  // Storage and analytics
  sl.registerLazySingleton<AnalyticsRepository>(
    () => AnalyticsRepositoryImpl(config: AppConfig()),
  );
  sl.registerLazySingleton<WebRTCRepository>(WebRTCRepositoryImpl.new);

  // Profile picture repository
  sl.registerLazySingleton<ProfilePictureRepository>(
    () => ProfilePictureRepositoryImpl(
      storageService: sl(),
      apiService: sl(),
    ),
  );

  // Availability repository
  sl.registerLazySingleton<AvailabilityRepository>(
    () => AvailabilityRepositoryImpl(),
  );

  // User settings repository
  sl.registerLazySingleton<UserSettingsRepository>(
    () => UserSettingsRepositoryImpl(localStorageService: sl()),
  );

  // Activity status repository
  sl.registerLazySingleton<ActivityStatusRepository>(
    () => ActivityStatusRepositoryImpl(userSettingsRepository: sl()),
  );

  // Report repository
  sl.registerLazySingleton<ReportRepository>(
    () => ReportRepositoryImpl(),
  );
}

/// Register services with proper dependencies
void _registerServices() {
  sl.registerLazySingleton<WebRTCService>(WebRTCService.new);
  sl.registerLazySingleton<FriendSelectionCache>(FriendSelectionCache.new);
  sl.registerLazySingleton<core.ConnectivityService>(() => ConnectivityServiceImpl());

  sl.registerLazySingleton<CallStateService>(() => CallStateServiceImpl(callBloc: sl()));
  // Note: StorageService is already registered in core_module.dart

  // Register abstract interfaces with direct implementations
  sl.registerLazySingleton<BlocNotificationService>(
    () => _BlocNotificationServiceImpl(
      notificationBloc: sl(),
    ),
  );

  sl.registerLazySingleton<CallNotificationService>(
    () => _CallNotificationServiceImpl(
      callBloc: sl(),
    ),
  );

  // Notification services following proper architecture
  sl.registerLazySingleton<NotificationServiceFCM>(
    () => NotificationServiceFCM(),
  );

  sl.registerLazySingleton<RealTimeNotificationService>(
    () => RealTimeNotificationService(
      contactsDataSource: sl(),
      httpDataSource: sl(),
      onNotificationReceived: (data) => sl<NotificationOrchestrator>().processNotification(data),
      onConnectionStatusChanged: (connected) => {
        // Handle connection status changes if needed
      },
    ),
  );

  // Dialog service (presentation layer) - register first
  sl.registerLazySingleton<DialogServiceImpl>(
    () => DialogServiceImpl(),
  );

  // Notification orchestrator (stateful business logic layer)
  sl.registerLazySingleton<NotificationOrchestrator>(
    () => NotificationOrchestrator(
      notificationRepository: sl(),
    ),
  );

  // Real-time service manager (singleton for app-wide coordination)
  sl.registerLazySingleton<RealTimeServiceManager>(
    () => RealTimeServiceManager.instance,
  );

  // Report Service
  sl.registerLazySingleton<ReportService>(
    () => ReportService(httpDataSource: sl()),
  );

  // Connectivity monitoring service (internet / backend reachability)
  sl.registerLazySingleton<ConnectivityServiceImpl>(ConnectivityServiceImpl.new);

  // Activity Status Service (singleton for app-wide activity tracking)
  sl.registerLazySingleton<ActivityStatusService>(
    () => ActivityStatusService.instance,
  );
}

/// Register BLoCs with proper dependencies
void _registerBlocs() {
  // Core BLoCs
  sl.registerFactory(() => NotificationBloc(notificationRepository: sl()));



  sl.registerLazySingleton(() => UserProfileBloc(userRepository: sl()));

  sl.registerFactory(ThemeBloc.new);

  // Bubble BLoCs
  sl.registerFactory(
    () => BubbleBloc(bubbleRepository: sl(), authRepository: sl()),
  );

  sl.registerFactory(() => ActiveBubbleBloc(repository: sl()));

  sl.registerFactory(() => BubbleHistoryBloc(repository: sl()));

  sl.registerFactory(
    () => ContactRequestBloc(repository: sl()),
  );

  sl.registerFactory(
    () => BubbleJoinRequestBloc(
      repository: sl(),
      userRepository: sl(),
    ),
  );

  sl.registerFactory(
    () => BubbleInviteRequestBloc(
      repository: sl(),
      userRepository: sl(),
    ),
  );

  sl.registerFactory(
    () => BubbleRequestBloc(bubbleRepository: sl()),
  );

  sl.registerFactory(
    () => BubbleKickoutRequestBloc(bubbleRepository: sl()),
  );

  // Social BLoCs
  sl.registerFactory(() => ContactsBloc(contactsRepository: sl(), authBloc: sl()));

  sl.registerFactory(() => FriendsBloc(userRepository: sl(), authBloc: sl()));

  sl.registerFactory(
    () => UnifiedProfileBloc(
      userRepository: sl(),
      bubbleRepository: sl(),
      contactRequestRepository: sl(),
      authBloc: sl(),
    ),
  );

  // Home navigation bloc (determines where to land after login)
  sl.registerFactory(() => HomeNavigationBloc(
        userRepository: sl(),
        activeBubbleRepository: sl(),
        authBloc: sl(),
      ));

  // Call BLoC
  sl.registerFactory(
    () => CallBloc(
      callRepository: sl<CallRepositoryExtended>(),
    ),
  );

  // Utility BLoCs
  sl.registerFactory(() => FriendSelectionBloc(
    repository: sl(),
    authRepository: sl(),
  ));

  // Profile picture BLoC
  sl.registerFactory(() => ProfilePictureBloc(
    profilePictureRepository: sl(),
    connectivityService: sl(),
  ));

  // Availability BLoC
  sl.registerFactory(() => AvailabilityBloc(availabilityRepository: sl()));

  // Activity Status BLoC
  sl.registerFactory(() => ActivityStatusBloc(activityStatusRepository: sl()));

  // Voice Recording BLoC
  sl.registerFactory(() => VoiceRecordingBloc(recordingRepository: sl()));

  // Report BLoC
  sl.registerFactory(() => ReportBloc(reportRepository: sl()));
}

/// Internal implementation of BlocNotificationService
class _BlocNotificationServiceImpl implements BlocNotificationService {
  final NotificationBloc _notificationBloc;

  _BlocNotificationServiceImpl({
    required NotificationBloc notificationBloc,
  }) : _notificationBloc = notificationBloc;

  @override
  void addNotification(Map<String, dynamic> notificationData) {
    try {
      final notification = Notification.fromJson(notificationData);
      _notificationBloc.add(AddNotificationEvent(notification: notification));
      LoggingService.info('BlocNotificationService: Notification added to bloc');
    } catch (e) {
      LoggingService.error('BlocNotificationService: Failed to add notification: $e');
    }
  }

  @override
  void markNotificationAsRead(String notificationId) {
    try {
      _notificationBloc.add(MarkNotificationAsRead(notificationId));
      LoggingService.info('BlocNotificationService: Notification $notificationId marked as read');
    } catch (e) {
      LoggingService.error('BlocNotificationService: Failed to mark notification as read: $e');
    }
  }

  @override
  void clearAllNotifications() {
    try {
      _notificationBloc.add(DeleteAllNotifications());
      LoggingService.info('BlocNotificationService: All notifications cleared');
    } catch (e) {
      LoggingService.error('BlocNotificationService: Failed to clear notifications: $e');
    }
  }

  @override
  void removeNotification(String notificationId) {
    try {
      _notificationBloc.add(DeleteNotification(notificationId));
      LoggingService.info('BlocNotificationService: Notification $notificationId removed');
    } catch (e) {
      LoggingService.error('BlocNotificationService: Failed to remove notification: $e');
    }
  }
}

/// Internal implementation of CallNotificationService
class _CallNotificationServiceImpl implements CallNotificationService {
  final CallBloc _callBloc;

  _CallNotificationServiceImpl({
    required CallBloc callBloc,
  }) : _callBloc = callBloc;

  @override
  void notifyIncomingCall({
    required String callId,
    required String callerId,
    required String callerName,
    required bool isGroup,
    required bool isVideoOffered,
    required bool isAudioOffered,
    required bool isScreenShareOffered,
    required Map<String, dynamic> remoteOfferSdp,
    String? callerAvatar,
    String? groupId,
    String? groupName,
    String? groupAvatarUrl,
  }) {
    try {
      _callBloc.add(ReceiveIncomingCallEvent(
        callId: callId,
        callerId: callerId,
        callerName: callerName,
        isGroup: isGroup,
        isVideoOffered: isVideoOffered,
        isAudioOffered: isAudioOffered,
        isScreenShareOffered: isScreenShareOffered,
        remoteOfferSdp: remoteOfferSdp,
        callerAvatar: callerAvatar,
        groupId: groupId,
        groupName: groupName,
        groupAvatarUrl: groupAvatarUrl,
      ));
      LoggingService.info('CallNotificationService: Incoming call notification dispatched');
    } catch (e) {
      LoggingService.error('CallNotificationService: Failed to notify incoming call: $e');
    }
  }

  @override
  void notifyCallAccepted({
    required String callId,
    required bool withVideo,
    required Map<String, dynamic> remoteOfferSdp,
    required String originalCallerId,
    required bool isGroupCall,
    String? groupId,
  }) {
    try {
      _callBloc.add(AcceptCallEvent(
        callId: callId,
        withVideo: withVideo,
        remoteOfferSdp: remoteOfferSdp,
        originalCallerId: originalCallerId,
        isGroupCall: isGroupCall,
        groupId: groupId,
      ));
      LoggingService.info('CallNotificationService: Call accepted notification dispatched');
    } catch (e) {
      LoggingService.error('CallNotificationService: Failed to notify call accepted: $e');
    }
  }

  @override
  void notifyCallRejected({
    required String callId,
    required String participantId,
    String? reason,
  }) {
    try {
      _callBloc.add(CallRejectedEvent(
        callId: callId,
        reason: reason ?? 'Call rejected',
      ));
      LoggingService.info('CallNotificationService: Call rejected notification dispatched');
    } catch (e) {
      LoggingService.error('CallNotificationService: Failed to notify call rejected: $e');
    }
  }

  @override
  void notifyCallEnded({
    required String callId,
    String? reason,
  }) {
    try {
      _callBloc.add(EndCallEvent(
        callId: callId,
      ));
      LoggingService.info('CallNotificationService: Call ended notification dispatched');
    } catch (e) {
      LoggingService.error('CallNotificationService: Failed to notify call ended: $e');
    }
  }

  @override
  void notifyParticipantJoined({
    required String callId,
    required String participantId,
    required String participantName,
  }) {
    try {
      // Note: Add appropriate event when available in CallBloc
      LoggingService.info('CallNotificationService: Participant joined notification dispatched');
    } catch (e) {
      LoggingService.error('CallNotificationService: Failed to notify participant joined: $e');
    }
  }

  @override
  void notifyParticipantLeft({
    required String callId,
    required String participantId,
    String? reason,
  }) {
    try {
      // Note: Add appropriate event when available in CallBloc
      LoggingService.info('CallNotificationService: Participant left notification dispatched');
    } catch (e) {
      LoggingService.error('CallNotificationService: Failed to notify participant left: $e');
    }
  }
}
