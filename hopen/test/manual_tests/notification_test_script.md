# Comprehensive Notification Testing Script

This document provides step-by-step instructions for manually testing all notification types in the Hopen app to ensure the unified `RealTimeNotificationService` is working correctly.

## Prerequisites

1. **Backend Services Running**: Ensure all backend services are running (MQTT broker, notification service, etc.)
2. **Two Test Devices/Accounts**: You'll need two test accounts to test notifications between users
3. **Network Connectivity**: Ensure devices have internet connectivity
4. **Push Notification Setup**: FCM should be properly configured

## Test Environment Setup

### 1. Start the App
```bash
cd hopen
flutter run --debug
```

### 2. Login with Test Accounts
- Device A: Login with `<EMAIL>`
- Device B: Login with `<EMAIL>`

### 3. Enable Debug Logging
- Check that debug logging is enabled to see MQTT5 connection status
- Look for logs like: `RealTimeNotificationService: MQTT5 connected successfully`

## Test Cases

### 🤝 Contact Request Notifications

#### Test 1: MQTT5 Real-Time Contact Request
**Objective**: Verify contact requests are delivered in real-time via MQTT5

**Steps**:
1. **Device A**: Navigate to search/discover users
2. **Device A**: Search for `test_user_2` and send a contact request
3. **Device B**: Should receive notification immediately (within 2-3 seconds)

**Expected Results**:
- ✅ Device B shows in-app notification banner
- ✅ Notification appears in notification center
- ✅ Notification badge count increases
- ✅ Notification contains correct sender name
- ✅ Tapping notification marks it as read

**Debug Verification**:
- Check logs for: `RealTimeNotificationService: Processing contact request notification`
- Verify MQTT topic: `hopen/users/{user_id}/contact-requests/{request_id}`

#### Test 2: HTTP Polling Fallback
**Objective**: Verify contact requests work when MQTT5 is unavailable

**Steps**:
1. **Device B**: Disconnect from WiFi/mobile data for 30 seconds
2. **Device A**: Send contact request while Device B is offline
3. **Device B**: Reconnect to internet
4. **Device B**: Wait for polling cycle (30 seconds max)

**Expected Results**:
- ✅ Device B receives notification via HTTP polling
- ✅ Notification appears with slight delay (up to 30 seconds)
- ✅ All notification features work normally

**Debug Verification**:
- Check logs for: `RealTimeNotificationService: Found X pending contact requests via polling`

#### Test 3: Push Notifications (Background)
**Objective**: Verify push notifications when app is backgrounded

**Steps**:
1. **Device B**: Put app in background (home button)
2. **Device A**: Send contact request
3. **Device B**: Should receive push notification

**Expected Results**:
- ✅ Push notification appears in system notification tray
- ✅ Notification has correct title and message
- ✅ Tapping notification opens app and shows notification center
- ✅ In-app notification is properly marked

### 🫧 Bubble Invite/Join Notifications

#### Test 4: Bubble Invite Notifications
**Objective**: Verify bubble invitation notifications work correctly

**Steps**:
1. **Device A**: Create a new bubble
2. **Device A**: Invite `test_user_2` to the bubble
3. **Device B**: Should receive bubble invite notification

**Expected Results**:
- ✅ Real-time notification delivery (MQTT5)
- ✅ Notification shows bubble name and inviter
- ✅ Notification category is `bubbleInvite`
- ✅ Tapping notification navigates to bubble invite screen

#### Test 5: Bubble Join Request Notifications
**Objective**: Verify bubble join request notifications

**Steps**:
1. **Device B**: Request to join Device A's bubble
2. **Device A**: Should receive join request notification

**Expected Results**:
- ✅ Real-time notification delivery
- ✅ Notification shows requester name and bubble name
- ✅ Proper notification categorization

### 📞 Call Notifications

#### Test 6: Incoming Call Notifications
**Objective**: Verify incoming call notifications trigger properly

**Steps**:
1. **Device A**: Initiate voice call to `test_user_2`
2. **Device B**: Should receive incoming call notification

**Expected Results**:
- ✅ Real-time call notification via MQTT5
- ✅ CallBloc receives proper call events
- ✅ Call UI appears immediately
- ✅ Caller information is displayed correctly

#### Test 7: Call State Notifications
**Objective**: Verify call state change notifications

**Steps**:
1. **Device A**: Start call with Device B
2. **Device B**: Accept the call
3. **Device A**: Should receive call accepted notification
4. **Device B**: End the call
5. **Device A**: Should receive call ended notification

**Expected Results**:
- ✅ All call state changes trigger notifications
- ✅ CallBloc state updates correctly
- ✅ UI reflects call state changes immediately

### 💬 Chat Message Notifications

#### Test 8: Real-Time Chat Notifications
**Objective**: Verify chat message notifications work in real-time

**Steps**:
1. **Device A**: Send message in shared bubble/chat
2. **Device B**: Should receive message notification immediately

**Expected Results**:
- ✅ Real-time message delivery via MQTT5
- ✅ Message appears in chat immediately
- ✅ Notification badge updates for unread messages

### 👥 Friendship Notifications

#### Test 9: Friendship Establishment
**Objective**: Verify friendship notifications when bubbles expire

**Steps**:
1. **Setup**: Create a bubble with both users
2. **Setup**: Wait for bubble to expire (or manually expire via backend)
3. **Both Devices**: Should receive friendship established notifications

**Expected Results**:
- ✅ Both users receive friendship notifications
- ✅ Notifications contain friend's information
- ✅ Users are now in each other's friends list

### 🔔 System Notifications

#### Test 10: System-Level Notifications
**Objective**: Verify system notifications work correctly

**Steps**:
1. **Backend**: Send system notification via admin panel
2. **Both Devices**: Should receive system notification

**Expected Results**:
- ✅ System notifications delivered to all users
- ✅ Proper categorization as system notifications
- ✅ High priority notifications appear prominently

### 🔄 Fallback and Recovery

#### Test 11: Network Interruption Recovery
**Objective**: Verify service recovers from network interruptions

**Steps**:
1. **Device B**: Disconnect from internet for 2 minutes
2. **Device A**: Send multiple notifications during disconnection
3. **Device B**: Reconnect to internet

**Expected Results**:
- ✅ MQTT5 reconnects automatically
- ✅ Missed notifications are retrieved via HTTP polling
- ✅ No duplicate notifications
- ✅ All notifications eventually received

#### Test 12: App Lifecycle Handling
**Objective**: Verify notifications work across app lifecycle states

**Steps**:
1. **Device B**: Put app in background
2. **Device A**: Send notification
3. **Device B**: Receive push notification, tap to open app
4. **Device B**: Put app in foreground
5. **Device A**: Send another notification

**Expected Results**:
- ✅ Background: Push notifications work
- ✅ Foreground: Real-time notifications work
- ✅ App resume: Pending notifications are fetched
- ✅ No notification loss during state transitions

### 🎯 UI and Interaction Tests

#### Test 13: Notification Center Functionality
**Objective**: Verify notification center UI works correctly

**Steps**:
1. **Device B**: Accumulate several notifications (don't read them)
2. **Device B**: Open notification center
3. **Device B**: Test all notification center features

**Expected Results**:
- ✅ All notifications displayed correctly
- ✅ Unread count is accurate
- ✅ "Mark all read" button works
- ✅ Individual notification tap works
- ✅ Notification deletion works
- ✅ Proper sorting (newest first)

#### Test 14: Notification Badge Updates
**Objective**: Verify notification badges update correctly

**Steps**:
1. **Device B**: Start with 0 unread notifications
2. **Device A**: Send 3 different types of notifications
3. **Device B**: Mark 1 notification as read
4. **Device B**: Mark all remaining as read

**Expected Results**:
- ✅ Badge shows 0 initially
- ✅ Badge increments to 3 after receiving notifications
- ✅ Badge decrements to 2 after marking one as read
- ✅ Badge shows 0 after marking all as read

### 📊 Performance and Reliability

#### Test 15: High Volume Notification Handling
**Objective**: Verify system handles multiple notifications correctly

**Steps**:
1. **Device A**: Send 10 notifications rapidly (different types)
2. **Device B**: Verify all notifications are received and processed

**Expected Results**:
- ✅ All notifications received
- ✅ No notifications lost or duplicated
- ✅ UI remains responsive
- ✅ Correct ordering maintained

#### Test 16: Memory and Resource Usage
**Objective**: Verify service doesn't leak resources

**Steps**:
1. **Both Devices**: Run app for extended period with notifications
2. **Monitor**: Check memory usage and performance

**Expected Results**:
- ✅ Memory usage remains stable
- ✅ No memory leaks detected
- ✅ MQTT connections are properly managed
- ✅ App remains responsive

## Test Results Documentation

### Test Summary Template

```
Test Date: ___________
Tester: ___________
App Version: ___________
Backend Version: ___________

Contact Request Notifications:
- [ ] MQTT5 Real-Time: PASS/FAIL
- [ ] HTTP Polling Fallback: PASS/FAIL  
- [ ] Push Notifications: PASS/FAIL

Bubble Notifications:
- [ ] Bubble Invites: PASS/FAIL
- [ ] Join Requests: PASS/FAIL

Call Notifications:
- [ ] Incoming Calls: PASS/FAIL
- [ ] Call State Changes: PASS/FAIL

Chat Notifications:
- [ ] Real-Time Messages: PASS/FAIL

Friendship Notifications:
- [ ] Friendship Establishment: PASS/FAIL

System Notifications:
- [ ] System Messages: PASS/FAIL

Fallback & Recovery:
- [ ] Network Recovery: PASS/FAIL
- [ ] App Lifecycle: PASS/FAIL

UI & Interaction:
- [ ] Notification Center: PASS/FAIL
- [ ] Badge Updates: PASS/FAIL

Performance:
- [ ] High Volume: PASS/FAIL
- [ ] Resource Usage: PASS/FAIL

Issues Found:
___________

Overall Status: PASS/FAIL
```

## Debugging Tips

### Common Issues and Solutions

1. **MQTT5 Connection Fails**
   - Check backend MQTT broker is running
   - Verify network connectivity
   - Check authentication credentials

2. **Notifications Not Received**
   - Verify user is subscribed to correct MQTT topics
   - Check HTTP polling is working as fallback
   - Verify notification service is running

3. **Push Notifications Not Working**
   - Check FCM configuration
   - Verify device tokens are registered
   - Check notification permissions

4. **UI Not Updating**
   - Verify BLoC events are being dispatched
   - Check notification repository is working
   - Verify UI is listening to BLoC state changes

### Debug Log Patterns to Look For

```
✅ Good Logs:
- "RealTimeNotificationService: MQTT5 connected successfully"
- "RealTimeNotificationService: Processing contact request notification"
- "BlocNotificationService: Notification added to bloc"

❌ Error Logs:
- "RealTimeNotificationService: MQTT5 initialization failed"
- "RealTimeNotificationService: Failed to dispatch notification"
- "RealTimeNotificationService: Max MQTT5 reconnection attempts reached"
```

This comprehensive test script ensures all aspects of the notification system are working correctly after our refactoring and integration work.
